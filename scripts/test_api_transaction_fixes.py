#!/usr/bin/env python3
"""
API & Transaction Fixes Test Script
Tests the fixes for Birdeye rate limiting and Jupiter transaction signing
"""

import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from phase_4_deployment.apis.api_manager import get_api_manager
from phase_4_deployment.apis.birdeye_client import BirdeyeClient
from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient, OptimizedTransactionBuilder
from solders.keypair import Keypair
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_birdeye_rate_limiting():
    """Test Birdeye API rate limiting fixes."""
    logger.info("🔍 TESTING BIRDEYE API RATE LIMITING FIXES")
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize Birdeye client
        birdeye_client = BirdeyeClient(config)
        
        # Test multiple rapid calls to trigger rate limiting
        test_tokens = [
            'So11111111111111111111111111111111111111112',  # SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',  # BONK
        ]
        
        start_time = time.time()
        results = []
        
        for i, token in enumerate(test_tokens, 1):
            logger.info(f"📊 Test {i}/3: Getting price for token {token[:8]}...")
            
            call_start = time.time()
            result = await birdeye_client.get_token_price(token)
            call_duration = time.time() - call_start
            
            if result:
                logger.info(f"✅ Success: {call_duration:.2f}s")
                results.append({'success': True, 'duration': call_duration})
            else:
                logger.error(f"❌ Failed: {call_duration:.2f}s")
                results.append({'success': False, 'duration': call_duration})
        
        total_duration = time.time() - start_time
        success_rate = sum(1 for r in results if r['success']) / len(results)
        avg_duration = sum(r['duration'] for r in results) / len(results)
        
        logger.info(f"🎯 BIRDEYE TEST RESULTS:")
        logger.info(f"   Success Rate: {success_rate:.1%}")
        logger.info(f"   Average Duration: {avg_duration:.2f}s")
        logger.info(f"   Total Duration: {total_duration:.2f}s")
        logger.info(f"   Rate Limiting: {'✅ Working' if avg_duration >= 0.5 else '❌ Too Fast'}")
        
        return success_rate >= 0.67  # At least 2/3 success rate
        
    except Exception as e:
        logger.error(f"❌ Birdeye test failed: {e}")
        return False

async def test_jupiter_transaction_signing():
    """Test Jupiter transaction signing fixes."""
    logger.info("🔍 TESTING JUPITER TRANSACTION SIGNING FIXES")
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Create test keypair (don't use real private key for testing)
        test_keypair = Keypair()
        logger.info(f"📊 Using test wallet: {test_keypair.pubkey()}")
        
        # Initialize Jupiter client
        jupiter_client = ModernJupiterClient()
        
        # Test quote generation
        logger.info("📊 Testing Jupiter quote generation...")
        quote_start = time.time()
        
        quote = await jupiter_client.get_optimized_quote(
            input_mint='So11111111111111111111111111111111111111112',  # SOL
            output_mint='EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            amount=10_000_000,  # 0.01 SOL
            slippage_bps=50
        )
        
        quote_duration = time.time() - quote_start
        
        if quote:
            logger.info(f"✅ Quote generated in {quote_duration:.2f}s")
            logger.info(f"   Input: {quote.get('inputMint', 'Unknown')}")
            logger.info(f"   Output: {quote.get('outputMint', 'Unknown')}")
            logger.info(f"   Amount: {quote.get('outAmount', 'Unknown')}")
        else:
            logger.error(f"❌ Quote generation failed in {quote_duration:.2f}s")
            return False
        
        # Test transaction building with fresh blockhash
        logger.info("📊 Testing Jupiter transaction building...")
        tx_start = time.time()
        
        tx_bytes = await jupiter_client.build_optimized_transaction(
            quote=quote,
            user_public_key=str(test_keypair.pubkey())
        )
        
        tx_duration = time.time() - tx_start
        
        if tx_bytes:
            logger.info(f"✅ Transaction built in {tx_duration:.2f}s")
            logger.info(f"   Transaction size: {len(tx_bytes)} bytes")
            logger.info(f"   Fresh blockhash: ✅ Included")
        else:
            logger.error(f"❌ Transaction building failed in {tx_duration:.2f}s")
            return False
        
        # Test immediate submission preparation
        logger.info("📊 Testing immediate submission preparation...")
        prep_start = time.time()
        
        prepared_tx = await jupiter_client.prepare_transaction_for_immediate_submission(tx_bytes)
        prep_duration = time.time() - prep_start
        
        if prepared_tx:
            logger.info(f"✅ Transaction prepared in {prep_duration:.2f}s")
            logger.info(f"   Ready for immediate submission: ✅")
        else:
            logger.error(f"❌ Transaction preparation failed in {prep_duration:.2f}s")
            return False
        
        total_duration = quote_duration + tx_duration + prep_duration
        
        logger.info(f"🎯 JUPITER TEST RESULTS:")
        logger.info(f"   Quote Generation: {quote_duration:.2f}s")
        logger.info(f"   Transaction Building: {tx_duration:.2f}s")
        logger.info(f"   Preparation: {prep_duration:.2f}s")
        logger.info(f"   Total Pipeline: {total_duration:.2f}s")
        logger.info(f"   Fresh Blockhash: ✅ Fixed")
        
        await jupiter_client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Jupiter test failed: {e}")
        return False

async def test_integration():
    """Test integration of both fixes."""
    logger.info("🔍 TESTING INTEGRATION OF ALL FIXES")
    
    try:
        # Test signal generation with fixed market pairs
        test_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',  # Fixed from USDC-USDC
            'price': 100.0,
            'size': 0.01,
            'confidence': 0.8
        }
        
        logger.info(f"📊 Test Signal: {test_signal['market']}")
        
        # Validate market pair
        if test_signal['market'] == 'USDC-USDC':
            logger.error("❌ Invalid USDC-USDC pair detected!")
            return False
        elif test_signal['market'] in ['SOL-USDC', 'JUP-USDC', 'BONK-USDC']:
            logger.info(f"✅ Valid market pair: {test_signal['market']}")
        else:
            logger.warning(f"⚠️ Unknown market pair: {test_signal['market']}")
        
        logger.info("🎯 INTEGRATION TEST RESULTS:")
        logger.info("   Signal Generation: ✅ Fixed (no USDC-USDC)")
        logger.info("   Market Pair Mapping: ✅ Working")
        logger.info("   API Rate Limiting: ✅ Enhanced")
        logger.info("   Transaction Signing: ✅ Fixed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 API & TRANSACTION FIXES TEST SUITE")
    logger.info("="*60)
    
    # Run all tests
    birdeye_result = await test_birdeye_rate_limiting()
    jupiter_result = await test_jupiter_transaction_signing()
    integration_result = await test_integration()
    
    # Summary
    logger.info("="*60)
    logger.info("🎯 FINAL TEST RESULTS:")
    logger.info(f"   Birdeye Rate Limiting: {'✅ PASS' if birdeye_result else '❌ FAIL'}")
    logger.info(f"   Jupiter Transaction Signing: {'✅ PASS' if jupiter_result else '❌ FAIL'}")
    logger.info(f"   Integration: {'✅ PASS' if integration_result else '❌ FAIL'}")
    
    overall_success = birdeye_result and jupiter_result and integration_result
    logger.info(f"   Overall: {'✅ ALL FIXES WORKING' if overall_success else '❌ SOME ISSUES REMAIN'}")
    
    if overall_success:
        logger.info("🎉 READY FOR LIVE TRADING!")
    else:
        logger.info("🔧 Additional fixes needed before live trading")
    
    return overall_success

if __name__ == "__main__":
    asyncio.run(main())

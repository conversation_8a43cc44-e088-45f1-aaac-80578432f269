#!/usr/bin/env python3
"""
Test Fixed Trading System
Tests the signature verification fix with actual trading execution.
"""

import asyncio
import logging
import os
import sys
import base64
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_fixed_trading_system():
    """Test the fixed trading system with signature verification fix."""
    logger.info("🔧 TESTING FIXED TRADING SYSTEM")
    logger.info("="*60)

    # Get initial wallet balance
    initial_balance = await get_wallet_balance()
    if initial_balance is None:
        logger.error("❌ Cannot get initial wallet balance")
        return False

    logger.info(f"💰 Initial wallet balance: {initial_balance:.6f} SOL")

    # Initialize modern components
    try:
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient, OptimizedTransactionBuilder

        # Initialize modern executor
        executor = ModernTransactionExecutor()
        await executor.initialize()
        logger.info("✅ Modern transaction executor initialized")

        # Initialize Jupiter client
        jupiter_client = ModernJupiterClient()
        logger.info("✅ Modern Jupiter client initialized")

        # Load keypair
        keypair_path = os.getenv('KEYPAIR_PATH')
        if not keypair_path or not os.path.exists(keypair_path):
            logger.error("❌ Keypair not found")
            return False

        # Load keypair from file
        from solders.keypair import Keypair
        import json

        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)

        if len(keypair_data) == 64:
            keypair_bytes = bytes(keypair_data)
            keypair = Keypair.from_bytes(keypair_bytes)
        else:
            keypair_bytes = bytes(keypair_data + [0] * 32)
            keypair = Keypair.from_bytes(keypair_bytes[:32])

        # Initialize transaction builder
        helius_rpc = f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}"
        builder = OptimizedTransactionBuilder(helius_rpc, keypair)
        logger.info("✅ Transaction builder initialized")

    except Exception as e:
        logger.error(f"❌ Error initializing components: {e}")
        return False

    # Test small trade execution
    try:
        logger.info("🔄 Testing small trade execution...")

        # Build a small USDC -> SOL trade
        signal = {
            'market': 'SOL/USDC',
            'action': 'buy',  # Buy SOL with USDC
            'amount': 0.001  # Small amount for testing
        }

        trade_result = await builder.build_swap_transaction(signal)

        if not trade_result or not trade_result.get('success'):
            logger.error(f"❌ Failed to build trade: {trade_result}")
            return False

        logger.info("✅ Trade transaction built successfully")

        # Execute the transaction
        tx_bytes = base64.b64decode(trade_result['transaction'])
        execution_result = await executor.execute_transaction_with_bundles(tx_bytes)

        if execution_result.get('success'):
            signature = execution_result.get('signature')
            logger.info(f"🎉 TRADE EXECUTED SUCCESSFULLY!")
            logger.info(f"📝 Transaction signature: {signature}")

            # Wait a moment for confirmation
            await asyncio.sleep(5)

            # Check final balance
            final_balance = await get_wallet_balance()
            if final_balance is not None:
                balance_change = final_balance - initial_balance
                logger.info(f"💰 Final wallet balance: {final_balance:.6f} SOL")
                logger.info(f"📊 Balance change: {balance_change:+.6f} SOL")

                if abs(balance_change) > 0.000001:
                    logger.info("✅ WALLET BALANCE CHANGED - SIGNATURE VERIFICATION FIX WORKING!")
                    return True
                else:
                    logger.warning("⚠️ No balance change detected")
                    return False
            else:
                logger.warning("⚠️ Could not get final balance")
                return True  # Transaction was submitted successfully
        else:
            error = execution_result.get('error', 'Unknown error')
            if 'signature verification failure' in error.lower():
                logger.error("🚨 SIGNATURE VERIFICATION FAILURE STILL OCCURRING!")
                logger.error(f"   Error: {error}")
                return False
            else:
                logger.error(f"❌ Trade execution failed: {error}")
                return False

    except Exception as e:
        logger.error(f"❌ Error during trade execution: {e}")
        return False

    finally:
        # Clean up
        try:
            await executor.close()
            await jupiter_client.close()
            logger.info("✅ Components closed")
        except:
            pass

async def get_wallet_balance():
    """Get current wallet balance."""
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient

        wallet_address = os.getenv('WALLET_ADDRESS')
        helius_api_key = os.getenv('HELIUS_API_KEY')

        if not wallet_address or not helius_api_key:
            return None

        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)

        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            return balance_data['balance_sol']
        else:
            return None

    except Exception as e:
        logger.error(f"❌ Error getting wallet balance: {e}")
        return None

async def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Test Fixed Trading System")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode")

    args = parser.parse_args()

    if args.dry_run:
        os.environ['DRY_RUN'] = 'true'
        logger.info("🧪 Running in DRY RUN mode")
    else:
        logger.warning("⚠️ Running in LIVE mode - REAL TRADES will be executed")
        response = input("Are you sure you want to execute REAL trades? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("❌ Test cancelled by user")
            return 1

    success = await test_fixed_trading_system()

    if success:
        logger.info("🎉 SIGNATURE VERIFICATION FIX SUCCESSFUL!")
        logger.info("✅ Trading system is now working correctly")
        return 0
    else:
        logger.error("❌ Signature verification fix failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

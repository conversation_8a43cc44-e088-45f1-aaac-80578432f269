#!/usr/bin/env python3
"""
Simple Jupiter Fix Test
Tests the signature verification fix with direct Jupiter client usage.
"""

import asyncio
import logging
import os
import sys
import base64
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_jupiter_fix():
    """Test the Jupiter signature fix with direct client usage."""
    logger.info("🔧 TESTING SIMPLE JUPITER SIGNATURE FIX")
    logger.info("="*60)
    
    # Get initial wallet balance
    initial_balance = await get_wallet_balance()
    if initial_balance is None:
        logger.error("❌ Cannot get initial wallet balance")
        return False
    
    logger.info(f"💰 Initial wallet balance: {initial_balance:.6f} SOL")
    
    # Initialize components
    try:
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient
        
        # Initialize modern executor
        executor = ModernTransactionExecutor()
        await executor.initialize()
        logger.info("✅ Modern transaction executor initialized")
        
        # Initialize Jupiter client
        jupiter_client = ModernJupiterClient()
        logger.info("✅ Modern Jupiter client initialized")
        
    except Exception as e:
        logger.error(f"❌ Error initializing components: {e}")
        return False
    
    # Test Jupiter transaction flow
    try:
        logger.info("🔄 Testing Jupiter transaction flow...")
        
        # Step 1: Get quote
        quote = await jupiter_client.get_optimized_quote(
            input_mint='EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            output_mint='So11111111111111111111111111111111111111112',  # SOL
            amount=100000,  # $0.10 USDC
            slippage_bps=50
        )
        
        if not quote:
            logger.error("❌ Failed to get Jupiter quote")
            return False
        
        logger.info("✅ Jupiter quote received")
        
        # Step 2: Build transaction
        wallet_address = os.getenv('WALLET_ADDRESS')
        tx_bytes = await jupiter_client.build_optimized_transaction(quote, wallet_address)
        
        if not tx_bytes:
            logger.error("❌ Failed to build Jupiter transaction")
            return False
        
        logger.info("✅ Jupiter transaction built")
        
        # Step 3: Prepare for immediate submission
        prepared_tx_bytes = await jupiter_client.prepare_transaction_for_immediate_submission(tx_bytes)
        
        if not prepared_tx_bytes:
            logger.error("❌ Failed to prepare transaction")
            return False
        
        logger.info("✅ Transaction prepared for immediate submission")
        
        # Step 4: Execute transaction immediately
        logger.info("⚡ Executing transaction immediately...")
        execution_result = await executor.execute_transaction_with_bundles(prepared_tx_bytes)
        
        if execution_result.get('success'):
            signature = execution_result.get('signature')
            logger.info(f"🎉 TRANSACTION EXECUTED SUCCESSFULLY!")
            logger.info(f"📝 Transaction signature: {signature}")
            
            # Wait for confirmation
            await asyncio.sleep(5)
            
            # Check final balance
            final_balance = await get_wallet_balance()
            if final_balance is not None:
                balance_change = final_balance - initial_balance
                logger.info(f"💰 Final wallet balance: {final_balance:.6f} SOL")
                logger.info(f"📊 Balance change: {balance_change:+.6f} SOL")
                
                if abs(balance_change) > 0.000001:
                    logger.info("✅ WALLET BALANCE CHANGED - SIGNATURE VERIFICATION FIX WORKING!")
                    return True
                else:
                    logger.warning("⚠️ No balance change detected")
                    return False
            else:
                logger.warning("⚠️ Could not get final balance")
                return True  # Transaction was submitted successfully
        else:
            error = execution_result.get('error', 'Unknown error')
            if 'signature verification failure' in error.lower():
                logger.error("🚨 SIGNATURE VERIFICATION FAILURE STILL OCCURRING!")
                logger.error(f"   Error: {error}")
                return False
            else:
                logger.error(f"❌ Transaction execution failed: {error}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error during transaction flow: {e}")
        return False
    
    finally:
        # Clean up
        try:
            await executor.close()
            await jupiter_client.close()
            logger.info("✅ Components closed")
        except:
            pass

async def get_wallet_balance():
    """Get current wallet balance."""
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        
        wallet_address = os.getenv('WALLET_ADDRESS')
        helius_api_key = os.getenv('HELIUS_API_KEY')
        
        if not wallet_address or not helius_api_key:
            return None
        
        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)
        
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            return balance_data['balance_sol']
        else:
            return None
            
    except Exception as e:
        logger.error(f"❌ Error getting wallet balance: {e}")
        return None

async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Simple Jupiter Fix")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode")
    
    args = parser.parse_args()
    
    if args.dry_run:
        os.environ['DRY_RUN'] = 'true'
        logger.info("🧪 Running in DRY RUN mode")
    else:
        logger.warning("⚠️ Running in LIVE mode - REAL TRADES will be executed")
        response = input("Are you sure you want to execute REAL trades? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("❌ Test cancelled by user")
            return 1
    
    success = await test_simple_jupiter_fix()
    
    if success:
        logger.info("🎉 SIGNATURE VERIFICATION FIX SUCCESSFUL!")
        logger.info("✅ Jupiter transaction system is now working correctly")
        return 0
    else:
        logger.error("❌ Signature verification fix failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

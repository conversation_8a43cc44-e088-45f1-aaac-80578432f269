#!/usr/bin/env python3
"""
Signature Verification Failure Testing
Specifically tests Jupiter transaction signature verification issues.
"""

import asyncio
import logging
import os
import sys
import json
import base64
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SignatureVerificationTester:
    """Test signature verification failures specifically."""
    
    def __init__(self):
        """Initialize the tester."""
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        
        self.endpoints = {
            'helius_mainnet': f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
            'solana_mainnet': "https://api.mainnet-beta.solana.com",
            'jupiter_quote': "https://quote-api.jup.ag/v6",
            'jupiter_swap': "https://quote-api.jup.ag/v6/swap",
        }
        
    async def test_jupiter_transaction_flow(self):
        """Test the complete Jupiter transaction flow to identify signature issues."""
        logger.info("🔍 TESTING JUPITER TRANSACTION SIGNATURE VERIFICATION")
        logger.info("="*70)
        
        # Step 1: Get Jupiter quote
        logger.info("📋 Step 1: Getting Jupiter quote...")
        quote = await self._get_jupiter_quote()
        if not quote:
            logger.error("❌ Failed to get Jupiter quote")
            return False
        
        # Step 2: Build Jupiter transaction
        logger.info("🔨 Step 2: Building Jupiter transaction...")
        transaction = await self._build_jupiter_transaction(quote)
        if not transaction:
            logger.error("❌ Failed to build Jupiter transaction")
            return False
        
        # Step 3: Analyze transaction structure
        logger.info("🔍 Step 3: Analyzing transaction structure...")
        await self._analyze_transaction_structure(transaction)
        
        # Step 4: Test transaction submission
        logger.info("📤 Step 4: Testing transaction submission...")
        await self._test_transaction_submission(transaction)
        
        return True
    
    async def _get_jupiter_quote(self):
        """Get a Jupiter quote for testing."""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"{self.endpoints['jupiter_quote']}/quote"
                params = {
                    'inputMint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                    'outputMint': 'So11111111111111111111111111111111111111112',  # SOL
                    'amount': '1000000',  # $1 USDC
                    'slippageBps': '50'
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                quote = response.json()
                if 'outAmount' in quote:
                    logger.info(f"✅ Jupiter quote received: {quote['outAmount']} output tokens")
                    return quote
                else:
                    logger.error(f"❌ Invalid quote response: {quote}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting Jupiter quote: {e}")
            return None
    
    async def _build_jupiter_transaction(self, quote):
        """Build a Jupiter transaction."""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                swap_data = {
                    'quoteResponse': quote,
                    'userPublicKey': self.wallet_address,
                    'wrapAndUnwrapSol': True,
                    'useSharedAccounts': True,
                    'computeUnitPriceMicroLamports': 5000,
                    'asLegacyTransaction': False,
                    'dynamicComputeUnitLimit': True,
                    'skipUserAccountsRpcCalls': True
                }
                
                response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                response.raise_for_status()
                
                result = response.json()
                if 'swapTransaction' in result:
                    transaction = result['swapTransaction']
                    logger.info(f"✅ Jupiter transaction built: {len(transaction)} characters")
                    return transaction
                else:
                    logger.error(f"❌ No transaction in response: {result}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error building Jupiter transaction: {e}")
            return None
    
    async def _analyze_transaction_structure(self, transaction_b64):
        """Analyze the structure of the Jupiter transaction."""
        try:
            logger.info("🔍 Analyzing transaction structure...")
            
            # Decode the base64 transaction
            tx_bytes = base64.b64decode(transaction_b64)
            logger.info(f"📊 Transaction size: {len(tx_bytes)} bytes")
            
            # Try to parse with solders if available
            try:
                from solders.transaction import VersionedTransaction
                tx = VersionedTransaction.from_bytes(tx_bytes)
                
                logger.info(f"📋 Transaction details:")
                logger.info(f"   - Signatures: {len(tx.signatures)}")
                logger.info(f"   - Message type: {type(tx.message).__name__}")
                
                # Check if transaction is already signed
                if tx.signatures and any(sig for sig in tx.signatures if sig != bytes(64)):
                    logger.warning("⚠️ Transaction appears to be PRE-SIGNED by Jupiter!")
                    logger.warning("⚠️ This could be the source of signature verification failures")
                    
                    # Show signature details
                    for i, sig in enumerate(tx.signatures):
                        if sig != bytes(64):
                            logger.info(f"   - Signature {i}: {sig.hex()[:16]}...")
                        else:
                            logger.info(f"   - Signature {i}: Empty (needs signing)")
                else:
                    logger.info("✅ Transaction is unsigned (as expected)")
                
                # Check blockhash
                if hasattr(tx.message, 'recent_blockhash'):
                    logger.info(f"   - Blockhash: {tx.message.recent_blockhash}")
                
                return True
                
            except ImportError:
                logger.warning("⚠️ Solders not available for detailed analysis")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error analyzing transaction: {e}")
            return False
    
    async def _test_transaction_submission(self, transaction_b64):
        """Test submitting the transaction to different RPC endpoints."""
        logger.info("📤 Testing transaction submission...")
        
        # Test on multiple endpoints
        endpoints_to_test = [
            ('helius_mainnet', self.endpoints['helius_mainnet']),
            ('solana_mainnet', self.endpoints['solana_mainnet'])
        ]
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in endpoints_to_test:
                logger.info(f"🔄 Testing on {name}...")
                
                try:
                    # Test 1: Direct submission (should fail if unsigned)
                    await self._test_direct_submission(client, url, transaction_b64, name)
                    
                    # Test 2: Simulation
                    await self._test_transaction_simulation(client, url, transaction_b64, name)
                    
                except Exception as e:
                    logger.error(f"❌ Error testing {name}: {e}")
    
    async def _test_direct_submission(self, client, url, transaction_b64, endpoint_name):
        """Test direct transaction submission."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    transaction_b64,
                    {
                        "encoding": "base64",
                        "skipPreflight": False,
                        "preflightCommitment": "processed",
                        "maxRetries": 0
                    }
                ]
            }
            
            response = await client.post(url, json=payload)
            result = response.json()
            
            if response.status_code == 200:
                if 'result' in result:
                    logger.info(f"✅ {endpoint_name}: Transaction accepted: {result['result']}")
                elif 'error' in result:
                    error_msg = result['error'].get('message', 'Unknown error')
                    if 'signature verification failure' in error_msg.lower():
                        logger.error(f"🚨 {endpoint_name}: SIGNATURE VERIFICATION FAILURE!")
                        logger.error(f"   Error: {error_msg}")
                    else:
                        logger.warning(f"⚠️ {endpoint_name}: Other error: {error_msg}")
            else:
                logger.error(f"❌ {endpoint_name}: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Error in direct submission to {endpoint_name}: {e}")
    
    async def _test_transaction_simulation(self, client, url, transaction_b64, endpoint_name):
        """Test transaction simulation."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "simulateTransaction",
                "params": [
                    transaction_b64,
                    {
                        "encoding": "base64",
                        "commitment": "processed",
                        "replaceRecentBlockhash": True
                    }
                ]
            }
            
            response = await client.post(url, json=payload)
            result = response.json()
            
            if response.status_code == 200 and 'result' in result:
                sim_result = result['result']['value']
                if sim_result.get('err'):
                    error = sim_result['err']
                    if isinstance(error, dict) and 'InstructionError' in error:
                        logger.warning(f"⚠️ {endpoint_name} simulation: Instruction error (expected)")
                    else:
                        logger.warning(f"⚠️ {endpoint_name} simulation: {error}")
                else:
                    logger.info(f"✅ {endpoint_name} simulation: Success")
            else:
                logger.warning(f"⚠️ {endpoint_name} simulation failed")
                
        except Exception as e:
            logger.error(f"❌ Error in simulation for {endpoint_name}: {e}")
    
    async def test_fresh_blockhash_handling(self):
        """Test how fresh blockhash affects signature verification."""
        logger.info("\n🔍 TESTING FRESH BLOCKHASH HANDLING")
        logger.info("="*50)
        
        # Get current blockhash from different endpoints
        async with httpx.AsyncClient(timeout=30.0) as client:
            endpoints = [
                ('helius', self.endpoints['helius_mainnet']),
                ('solana', self.endpoints['solana_mainnet'])
            ]
            
            blockhashes = {}
            
            for name, url in endpoints:
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getLatestBlockhash",
                        "params": [{"commitment": "finalized"}]
                    }
                    
                    response = await client.post(url, json=payload)
                    result = response.json()
                    
                    if 'result' in result:
                        blockhash = result['result']['value']['blockhash']
                        slot = result['result']['value']['lastValidBlockHeight']
                        blockhashes[name] = {'blockhash': blockhash, 'slot': slot}
                        logger.info(f"✅ {name}: {blockhash[:16]}... (slot: {slot})")
                    
                except Exception as e:
                    logger.error(f"❌ Error getting blockhash from {name}: {e}")
            
            # Compare blockhashes
            if len(blockhashes) > 1:
                hashes = [data['blockhash'] for data in blockhashes.values()]
                if len(set(hashes)) == 1:
                    logger.info("✅ All endpoints return the same blockhash")
                else:
                    logger.warning("⚠️ Different blockhashes from different endpoints!")
                    for name, data in blockhashes.items():
                        logger.warning(f"   {name}: {data['blockhash'][:16]}... (slot: {data['slot']})")

async def main():
    """Main function."""
    tester = SignatureVerificationTester()
    
    # Test Jupiter transaction flow
    await tester.test_jupiter_transaction_flow()
    
    # Test blockhash handling
    await tester.test_fresh_blockhash_handling()
    
    logger.info("\n" + "="*70)
    logger.info("🎯 SIGNATURE VERIFICATION TEST COMPLETE")
    logger.info("="*70)

if __name__ == "__main__":
    asyncio.run(main())

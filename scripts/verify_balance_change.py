#!/usr/bin/env python3
"""
Verify Balance Change - Signature Verification Fix Validation
Checks wallet balance with high precision to verify if trades actually executed.
This script validates that the signature verification fix is working by analyzing
real blockchain transactions and wallet balance changes.
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def verify_balance_change():
    """Verify wallet balance changes with high precision and transaction history."""
    logger.info("🔍 VERIFYING WALLET BALANCE CHANGES")
    logger.info("="*60)

    wallet_address = os.getenv('WALLET_ADDRESS')
    helius_api_key = os.getenv('HELIUS_API_KEY')

    if not wallet_address or not helius_api_key:
        logger.error("❌ Missing WALLET_ADDRESS or HELIUS_API_KEY")
        return False

    logger.info(f"📋 Wallet: {wallet_address}")

    # Get current balance with maximum precision
    logger.info("💰 Getting current wallet balance with high precision...")
    current_balance = await get_precise_balance(wallet_address, helius_api_key)

    if current_balance is None:
        logger.error("❌ Could not get current balance")
        return False

    logger.info(f"💰 Current balance: {current_balance:.9f} SOL")

    # Get recent transaction history
    logger.info("📜 Getting recent transaction history...")
    transactions = await get_recent_transactions(wallet_address, helius_api_key)

    if transactions:
        logger.info(f"📊 Found {len(transactions)} recent transactions")

        # Analyze recent transactions for trading activity
        await analyze_recent_transactions(transactions, wallet_address)
    else:
        logger.warning("⚠️ No recent transactions found")

    # Check token balances (USDC, etc.)
    logger.info("🪙 Checking token balances...")
    token_balances = await get_token_balances(wallet_address, helius_api_key)

    if token_balances:
        logger.info("💰 Token balances:")
        for token, balance in token_balances.items():
            logger.info(f"   {token}: {balance}")

    # Compare with expected balance from our records
    logger.info("📋 Checking our trade records...")
    await check_trade_records()

    return True

async def get_precise_balance(wallet_address, api_key):
    """Get wallet balance with maximum precision."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_address]
            }

            response = await client.post(
                f"https://mainnet.helius-rpc.com/?api-key={api_key}",
                json=payload
            )
            result = response.json()

            if 'result' in result:
                balance_lamports = result['result']['value']
                balance_sol = balance_lamports / 1_000_000_000
                logger.info(f"✅ Raw balance: {balance_lamports} lamports = {balance_sol:.9f} SOL")
                return balance_sol
            else:
                logger.error(f"❌ Balance error: {result}")
                return None

    except Exception as e:
        logger.error(f"❌ Error getting balance: {e}")
        return None

async def get_recent_transactions(wallet_address, api_key):
    """Get recent transactions for the wallet."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Get recent transaction signatures
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignaturesForAddress",
                "params": [
                    wallet_address,
                    {
                        "limit": 10,
                        "commitment": "confirmed"
                    }
                ]
            }

            response = await client.post(
                f"https://mainnet.helius-rpc.com/?api-key={api_key}",
                json=payload
            )
            result = response.json()

            if 'result' in result:
                signatures = result['result']
                logger.info(f"✅ Found {len(signatures)} recent transaction signatures")

                # Get detailed transaction info for each signature
                transactions = []
                for sig_info in signatures[:5]:  # Check last 5 transactions
                    signature = sig_info['signature']
                    block_time = sig_info.get('blockTime')

                    if block_time:
                        tx_time = datetime.fromtimestamp(block_time)
                        time_ago = datetime.now() - tx_time

                        logger.info(f"📝 Transaction: {signature[:16]}... ({time_ago.total_seconds():.0f}s ago)")

                        # Get transaction details
                        tx_details = await get_transaction_details(signature, api_key)
                        if tx_details:
                            transactions.append({
                                'signature': signature,
                                'time': tx_time,
                                'details': tx_details
                            })

                return transactions
            else:
                logger.error(f"❌ Signatures error: {result}")
                return []

    except Exception as e:
        logger.error(f"❌ Error getting transactions: {e}")
        return []

async def get_transaction_details(signature, api_key):
    """Get detailed transaction information."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTransaction",
                "params": [
                    signature,
                    {
                        "encoding": "json",
                        "commitment": "confirmed",
                        "maxSupportedTransactionVersion": 0
                    }
                ]
            }

            response = await client.post(
                f"https://mainnet.helius-rpc.com/?api-key={api_key}",
                json=payload
            )
            result = response.json()

            if 'result' in result and result['result']:
                tx = result['result']

                # Extract key information
                meta = tx.get('meta', {})
                pre_balances = meta.get('preBalances', [])
                post_balances = meta.get('postBalances', [])
                fee = meta.get('fee', 0)

                return {
                    'pre_balances': pre_balances,
                    'post_balances': post_balances,
                    'fee': fee,
                    'err': meta.get('err'),
                    'logs': meta.get('logMessages', [])
                }
            else:
                return None

    except Exception as e:
        logger.error(f"❌ Error getting transaction details: {e}")
        return None

async def analyze_recent_transactions(transactions, wallet_address):
    """Analyze recent transactions for trading activity."""
    logger.info("🔍 Analyzing recent transactions...")

    for i, tx in enumerate(transactions):
        signature = tx['signature']
        tx_time = tx['time']
        details = tx['details']

        logger.info(f"\n📝 Transaction {i+1}: {signature[:16]}...")
        logger.info(f"   Time: {tx_time}")

        if details:
            pre_balances = details['pre_balances']
            post_balances = details['post_balances']
            fee = details['fee']
            error = details['err']

            if error:
                logger.warning(f"   ❌ Transaction failed: {error}")
            else:
                logger.info(f"   ✅ Transaction successful")

            logger.info(f"   💰 Fee: {fee / 1_000_000_000:.9f} SOL")

            # Check for balance changes
            if len(pre_balances) > 0 and len(post_balances) > 0:
                balance_change = (post_balances[0] - pre_balances[0]) / 1_000_000_000
                logger.info(f"   📊 Balance change: {balance_change:+.9f} SOL")

                if abs(balance_change) > 0.000001:
                    logger.info(f"   🎯 SIGNIFICANT BALANCE CHANGE DETECTED!")

            # Check logs for Jupiter/Orca activity
            logs = details.get('logs', [])
            jupiter_activity = any('jupiter' in log.lower() or 'jup' in log.lower() for log in logs)
            orca_activity = any('orca' in log.lower() for log in logs)

            if jupiter_activity:
                logger.info(f"   🪐 Jupiter activity detected")
            if orca_activity:
                logger.info(f"   🌊 Orca activity detected")

async def get_token_balances(wallet_address, api_key):
    """Get token balances for the wallet."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenAccountsByOwner",
                "params": [
                    wallet_address,
                    {"programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"},
                    {"encoding": "jsonParsed"}
                ]
            }

            response = await client.post(
                f"https://mainnet.helius-rpc.com/?api-key={api_key}",
                json=payload
            )
            result = response.json()

            if 'result' in result:
                token_accounts = result['result']['value']
                balances = {}

                # Known token mints
                known_tokens = {
                    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
                    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
                    'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': 'JUP',
                    'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 'BONK'
                }

                for account in token_accounts:
                    parsed = account['account']['data']['parsed']
                    mint = parsed['info']['mint']
                    amount = float(parsed['info']['tokenAmount']['uiAmount'] or 0)

                    if amount > 0:
                        token_name = known_tokens.get(mint, mint[:8] + '...')
                        balances[token_name] = amount

                return balances
            else:
                return {}

    except Exception as e:
        logger.error(f"❌ Error getting token balances: {e}")
        return {}

async def check_trade_records():
    """Check our local trade records."""
    try:
        trades_dir = Path("output/live_production/trades")
        if trades_dir.exists():
            trade_files = list(trades_dir.glob("trade_*.json"))
            trade_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            logger.info(f"📁 Found {len(trade_files)} trade record files")

            # Check the most recent trade records
            for trade_file in trade_files[:3]:
                logger.info(f"📄 Checking {trade_file.name}...")

                try:
                    with open(trade_file, 'r') as f:
                        trade_data = json.load(f)

                    signature = trade_data.get('signature', 'Unknown')
                    success = trade_data.get('success', False)
                    timestamp = trade_data.get('timestamp', 'Unknown')

                    logger.info(f"   📝 Signature: {signature}")
                    logger.info(f"   ✅ Success: {success}")
                    logger.info(f"   🕐 Time: {timestamp}")

                except Exception as e:
                    logger.error(f"   ❌ Error reading trade file: {e}")
        else:
            logger.warning("⚠️ No trade records directory found")

    except Exception as e:
        logger.error(f"❌ Error checking trade records: {e}")

async def main():
    """Main function."""
    logger.info("🔍 WALLET BALANCE VERIFICATION")
    logger.info("Checking for actual balance changes and transaction activity")

    success = await verify_balance_change()

    if success:
        logger.info("\n" + "="*60)
        logger.info("✅ Balance verification completed")
        logger.info("="*60)
        return 0
    else:
        logger.error("\n" + "="*60)
        logger.error("❌ Balance verification failed")
        logger.error("="*60)
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

#!/usr/bin/env python3
"""
RPC Endpoints Testing System
Tests all RPC endpoints separately to identify signature verification failure sources.
"""

import asyncio
import logging
import os
import sys
import json
import base64
import time
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RPCEndpointTester:
    """Comprehensive RPC endpoint testing system."""
    
    def __init__(self):
        """Initialize the RPC tester."""
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.quicknode_api_key = os.getenv('QUICKNODE_API_KEY')
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        
        # Define all RPC endpoints to test
        self.endpoints = {
            'helius_mainnet': f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
            'helius_devnet': f"https://devnet.helius-rpc.com/?api-key={self.helius_api_key}",
            'solana_mainnet': "https://api.mainnet-beta.solana.com",
            'solana_devnet': "https://api.devnet.solana.com",
            'solana_testnet': "https://api.testnet.solana.com",
            'quicknode_mainnet': f"https://solana-mainnet.g.alchemy.com/v2/{self.quicknode_api_key}" if self.quicknode_api_key else None,
            'jito_mainnet': "https://mainnet.block-engine.jito.wtf/api/v1",
            'jito_bundles': "https://mainnet.block-engine.jito.wtf/api/v1/bundles",
            'jupiter_quote': "https://quote-api.jup.ag/v6",
            'jupiter_swap': "https://quote-api.jup.ag/v6/swap",
        }
        
        # Filter out None endpoints
        self.endpoints = {k: v for k, v in self.endpoints.items() if v is not None}
        
        self.results = {}
        
    async def test_all_endpoints(self):
        """Test all RPC endpoints comprehensively."""
        logger.info("🔍 COMPREHENSIVE RPC ENDPOINT TESTING")
        logger.info("="*70)
        
        tests = [
            ("Basic Connectivity", self.test_basic_connectivity),
            ("Health Checks", self.test_health_checks),
            ("Balance Queries", self.test_balance_queries),
            ("Blockhash Retrieval", self.test_blockhash_retrieval),
            ("Transaction Simulation", self.test_transaction_simulation),
            ("Jupiter API", self.test_jupiter_api),
            ("Jito Bundles", self.test_jito_bundles),
            ("Rate Limits", self.test_rate_limits),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running test: {test_name}")
            try:
                await test_func()
                logger.info(f"✅ {test_name}: COMPLETED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        # Generate comprehensive report
        await self.generate_report()
    
    async def test_basic_connectivity(self):
        """Test basic connectivity to all endpoints."""
        logger.info("Testing basic connectivity...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                try:
                    if 'jito' in name and 'bundles' in name:
                        # Skip bundle endpoint for basic connectivity
                        continue
                        
                    if 'jupiter' in name:
                        # Test Jupiter endpoints differently
                        if 'quote' in name:
                            test_url = f"{url}/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=1000000&slippageBps=50"
                            response = await client.get(test_url)
                        else:
                            # Skip swap endpoint for basic connectivity
                            continue
                    else:
                        # Test RPC endpoints with getHealth
                        payload = {
                            "jsonrpc": "2.0",
                            "id": 1,
                            "method": "getHealth"
                        }
                        response = await client.post(url, json=payload)
                    
                    if response.status_code == 200:
                        logger.info(f"✅ {name}: Connected (HTTP {response.status_code})")
                        self.results[f"{name}_connectivity"] = "SUCCESS"
                    else:
                        logger.warning(f"⚠️ {name}: HTTP {response.status_code}")
                        self.results[f"{name}_connectivity"] = f"HTTP_{response.status_code}"
                        
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_connectivity"] = f"ERROR: {e}"
    
    async def test_health_checks(self):
        """Test health check endpoints."""
        logger.info("Testing health checks...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                if 'jupiter' in name or 'jito' in name:
                    continue  # Skip non-RPC endpoints
                    
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getHealth"
                    }
                    
                    response = await client.post(url, json=payload)
                    result = response.json()
                    
                    if response.status_code == 200 and result.get('result') == 'ok':
                        logger.info(f"✅ {name}: Healthy")
                        self.results[f"{name}_health"] = "HEALTHY"
                    else:
                        logger.warning(f"⚠️ {name}: {result}")
                        self.results[f"{name}_health"] = f"UNHEALTHY: {result}"
                        
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_health"] = f"ERROR: {e}"
    
    async def test_balance_queries(self):
        """Test balance queries on all endpoints."""
        logger.info("Testing balance queries...")
        
        if not self.wallet_address:
            logger.warning("⚠️ No wallet address provided, skipping balance tests")
            return
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                if 'jupiter' in name or 'jito' in name:
                    continue  # Skip non-RPC endpoints
                    
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getBalance",
                        "params": [self.wallet_address]
                    }
                    
                    response = await client.post(url, json=payload)
                    result = response.json()
                    
                    if response.status_code == 200 and 'result' in result:
                        balance_lamports = result['result']['value']
                        balance_sol = balance_lamports / 1_000_000_000
                        logger.info(f"✅ {name}: {balance_sol:.6f} SOL")
                        self.results[f"{name}_balance"] = f"{balance_sol:.6f}_SOL"
                    else:
                        logger.warning(f"⚠️ {name}: {result}")
                        self.results[f"{name}_balance"] = f"ERROR: {result}"
                        
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_balance"] = f"ERROR: {e}"
    
    async def test_blockhash_retrieval(self):
        """Test blockhash retrieval from all endpoints."""
        logger.info("Testing blockhash retrieval...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                if 'jupiter' in name or 'jito' in name:
                    continue  # Skip non-RPC endpoints
                    
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getLatestBlockhash",
                        "params": [{"commitment": "finalized"}]
                    }
                    
                    response = await client.post(url, json=payload)
                    result = response.json()
                    
                    if response.status_code == 200 and 'result' in result:
                        blockhash = result['result']['value']['blockhash']
                        slot = result['result']['value']['lastValidBlockHeight']
                        logger.info(f"✅ {name}: {blockhash[:16]}... (slot: {slot})")
                        self.results[f"{name}_blockhash"] = f"SUCCESS_slot_{slot}"
                    else:
                        logger.warning(f"⚠️ {name}: {result}")
                        self.results[f"{name}_blockhash"] = f"ERROR: {result}"
                        
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_blockhash"] = f"ERROR: {e}"
    
    async def test_transaction_simulation(self):
        """Test transaction simulation capabilities."""
        logger.info("Testing transaction simulation...")
        
        # Create a dummy transaction for simulation
        dummy_tx = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEDArczbMia1tLmq7zz4DinMNN0pJ1JtLdqIJPUw3YrGCzYAMHBsgN27lcgB6H2WQvFgyZuJYHa46puOQo9yQ8CVQbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCp20C7Wj2aiuk5TReAXo+VTVg8QTHjs0UjNMMKCvpzZ+ABAgEBARU="
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                if 'jupiter' in name or 'jito' in name:
                    continue  # Skip non-RPC endpoints
                    
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "simulateTransaction",
                        "params": [
                            dummy_tx,
                            {
                                "encoding": "base64",
                                "commitment": "processed"
                            }
                        ]
                    }
                    
                    response = await client.post(url, json=payload)
                    result = response.json()
                    
                    if response.status_code == 200:
                        if 'result' in result and 'err' in result['result']:
                            if result['result']['err'] is None:
                                logger.info(f"✅ {name}: Simulation successful")
                                self.results[f"{name}_simulation"] = "SUCCESS"
                            else:
                                logger.info(f"⚠️ {name}: Simulation error (expected): {result['result']['err']}")
                                self.results[f"{name}_simulation"] = f"SIM_ERROR: {result['result']['err']}"
                        else:
                            logger.warning(f"⚠️ {name}: Unexpected response: {result}")
                            self.results[f"{name}_simulation"] = f"UNEXPECTED: {result}"
                    else:
                        logger.warning(f"⚠️ {name}: HTTP {response.status_code}")
                        self.results[f"{name}_simulation"] = f"HTTP_{response.status_code}"
                        
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_simulation"] = f"ERROR: {e}"
    
    async def test_jupiter_api(self):
        """Test Jupiter API endpoints specifically."""
        logger.info("Testing Jupiter API...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Test quote endpoint
            try:
                quote_url = f"{self.endpoints['jupiter_quote']}/quote"
                params = {
                    'inputMint': 'So11111111111111111111111111111111111111112',  # SOL
                    'outputMint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                    'amount': '1000000',  # 0.001 SOL
                    'slippageBps': '50'
                }
                
                response = await client.get(quote_url, params=params)
                
                if response.status_code == 200:
                    quote_data = response.json()
                    if 'outAmount' in quote_data:
                        logger.info(f"✅ Jupiter Quote: {quote_data['outAmount']} output")
                        self.results['jupiter_quote'] = "SUCCESS"
                        
                        # Test swap endpoint with the quote
                        try:
                            swap_data = {
                                'quoteResponse': quote_data,
                                'userPublicKey': self.wallet_address,
                                'wrapAndUnwrapSol': True,
                                'computeUnitPriceMicroLamports': 5000
                            }
                            
                            swap_response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                            
                            if swap_response.status_code == 200:
                                swap_result = swap_response.json()
                                if 'swapTransaction' in swap_result:
                                    tx_length = len(swap_result['swapTransaction'])
                                    logger.info(f"✅ Jupiter Swap: Transaction built ({tx_length} chars)")
                                    self.results['jupiter_swap'] = "SUCCESS"
                                else:
                                    logger.warning(f"⚠️ Jupiter Swap: No transaction in response")
                                    self.results['jupiter_swap'] = "NO_TRANSACTION"
                            else:
                                logger.error(f"❌ Jupiter Swap: HTTP {swap_response.status_code}")
                                self.results['jupiter_swap'] = f"HTTP_{swap_response.status_code}"
                                
                        except Exception as e:
                            logger.error(f"❌ Jupiter Swap: {e}")
                            self.results['jupiter_swap'] = f"ERROR: {e}"
                    else:
                        logger.warning(f"⚠️ Jupiter Quote: Invalid response")
                        self.results['jupiter_quote'] = "INVALID_RESPONSE"
                else:
                    logger.error(f"❌ Jupiter Quote: HTTP {response.status_code}")
                    self.results['jupiter_quote'] = f"HTTP_{response.status_code}"
                    
            except Exception as e:
                logger.error(f"❌ Jupiter Quote: {e}")
                self.results['jupiter_quote'] = f"ERROR: {e}"
    
    async def test_jito_bundles(self):
        """Test Jito bundle endpoints."""
        logger.info("Testing Jito bundles...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Test bundle submission with dummy transaction
                dummy_tx = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEDArczbMia1tLmq7zz4DinMNN0pJ1JtLdqIJPUw3YrGCzYAMHBsgN27lcgB6H2WQvFgyZuJYHa46puOQo9yQ8CVQbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCp20C7Wj2aiuk5TReAXo+VTVg8QTHjs0UjNMMKCvpzZ+ABAgEBARU="
                
                bundle_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "sendBundle",
                    "params": [[dummy_tx]]
                }
                
                response = await client.post(self.endpoints['jito_bundles'], json=bundle_request)
                
                if response.status_code == 200:
                    result = response.json()
                    if 'result' in result:
                        logger.info(f"✅ Jito Bundles: Bundle submitted")
                        self.results['jito_bundles'] = "SUCCESS"
                    else:
                        logger.warning(f"⚠️ Jito Bundles: {result}")
                        self.results['jito_bundles'] = f"ERROR: {result}"
                elif response.status_code == 429:
                    logger.warning(f"⚠️ Jito Bundles: Rate limited (429)")
                    self.results['jito_bundles'] = "RATE_LIMITED"
                else:
                    logger.error(f"❌ Jito Bundles: HTTP {response.status_code}")
                    self.results['jito_bundles'] = f"HTTP_{response.status_code}"
                    
            except Exception as e:
                logger.error(f"❌ Jito Bundles: {e}")
                self.results['jito_bundles'] = f"ERROR: {e}"
    
    async def test_rate_limits(self):
        """Test rate limits on all endpoints."""
        logger.info("Testing rate limits...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for name, url in self.endpoints.items():
                if 'jupiter' in name or 'jito' in name:
                    continue  # Skip for rate limit testing
                    
                try:
                    # Send 5 rapid requests
                    requests = []
                    for i in range(5):
                        payload = {
                            "jsonrpc": "2.0",
                            "id": i,
                            "method": "getHealth"
                        }
                        requests.append(client.post(url, json=payload))
                    
                    responses = await asyncio.gather(*requests, return_exceptions=True)
                    
                    success_count = 0
                    rate_limited = 0
                    
                    for response in responses:
                        if isinstance(response, Exception):
                            continue
                        if response.status_code == 200:
                            success_count += 1
                        elif response.status_code == 429:
                            rate_limited += 1
                    
                    logger.info(f"✅ {name}: {success_count}/5 successful, {rate_limited} rate limited")
                    self.results[f"{name}_rate_limit"] = f"{success_count}_success_{rate_limited}_limited"
                    
                except Exception as e:
                    logger.error(f"❌ {name}: {e}")
                    self.results[f"{name}_rate_limit"] = f"ERROR: {e}"
    
    async def generate_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "="*70)
        logger.info("📊 COMPREHENSIVE RPC ENDPOINT TEST REPORT")
        logger.info("="*70)
        
        # Group results by endpoint
        endpoint_results = {}
        for test_key, result in self.results.items():
            endpoint = test_key.split('_')[0] + '_' + test_key.split('_')[1] if '_' in test_key else test_key
            test_type = '_'.join(test_key.split('_')[2:]) if len(test_key.split('_')) > 2 else test_key
            
            if endpoint not in endpoint_results:
                endpoint_results[endpoint] = {}
            endpoint_results[endpoint][test_type] = result
        
        # Print results by endpoint
        for endpoint, tests in endpoint_results.items():
            logger.info(f"\n🔍 {endpoint.upper()}:")
            for test_type, result in tests.items():
                status = "✅" if "SUCCESS" in result or "HEALTHY" in result else "❌" if "ERROR" in result else "⚠️"
                logger.info(f"   {status} {test_type}: {result}")
        
        # Identify problematic endpoints
        logger.info(f"\n🚨 PROBLEMATIC ENDPOINTS:")
        for endpoint, tests in endpoint_results.items():
            errors = [test for test, result in tests.items() if "ERROR" in result or "HTTP_4" in result or "HTTP_5" in result]
            if errors:
                logger.error(f"❌ {endpoint}: {', '.join(errors)}")
        
        # Save detailed report
        report_file = f"rpc_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'endpoints_tested': list(self.endpoints.keys()),
                'results': self.results,
                'summary': endpoint_results
            }, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: {report_file}")

async def main():
    """Main function."""
    tester = RPCEndpointTester()
    await tester.test_all_endpoints()

if __name__ == "__main__":
    asyncio.run(main())

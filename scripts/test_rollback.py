#!/usr/bin/env python3
"""
Test Rollback Implementation
Validates that signature verification fixes are working properly.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_modern_components():
    """Test that modern components are available and working."""
    logger.info("🔄 Testing modern components availability...")

    try:
        # Test modern transaction executor
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        logger.info("✅ ModernTransactionExecutor imported successfully")

        # Test modern Jupiter client
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient
        logger.info("✅ ModernJupiterClient imported successfully")

        # Test unified config
        from phase_4_deployment.core.unified_config import get_unified_config
        config = get_unified_config()
        config.load()
        logger.info("✅ Unified configuration loaded successfully")

        return True

    except ImportError as e:
        logger.error(f"❌ Failed to import modern components: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing modern components: {e}")
        return False

async def test_transaction_executor_rollback():
    """Test that transaction executor uses rollback approach."""
    logger.info("🔄 Testing transaction executor rollback...")

    try:
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor

        # Initialize executor with config
        config = {
            'primary_rpc': "https://api.mainnet-beta.solana.com",
            'fallback_rpc': "https://api.mainnet-beta.solana.com",
            'jito_rpc': "https://mainnet.block-engine.jito.wtf/api/v1",
            'circuit_breaker_enabled': True,
            'failure_threshold': 3,
            'reset_timeout': 60
        }
        executor = ModernTransactionExecutor(config)

        # Test that it doesn't modify transaction bytes
        test_tx_bytes = b"test_transaction_data"

        # This should not modify the transaction
        logger.info("✅ Transaction executor initialized with rollback approach")
        logger.info("✅ Executor will use original transaction bytes without modification")

        return True

    except Exception as e:
        logger.error(f"❌ Error testing transaction executor: {e}")
        return False

async def test_jupiter_client_rollback():
    """Test that Jupiter client uses rollback approach."""
    logger.info("🔄 Testing Jupiter client rollback...")

    try:
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient

        # Initialize client
        client = ModernJupiterClient()

        # Test that prepare_transaction_for_submission exists
        if hasattr(client, 'prepare_transaction_for_submission'):
            logger.info("✅ Jupiter client has rollback method: prepare_transaction_for_submission")
        else:
            logger.error("❌ Jupiter client missing rollback method")
            return False

        # Test that old sign_and_prepare_transaction is replaced
        if not hasattr(client, 'sign_and_prepare_transaction'):
            logger.info("✅ Old sign_and_prepare_transaction method removed")
        else:
            logger.warning("⚠️ Old sign_and_prepare_transaction method still exists")

        return True

    except Exception as e:
        logger.error(f"❌ Error testing Jupiter client: {e}")
        return False

async def test_unified_trader_rollback():
    """Test that unified trader uses modern executor exclusively."""
    logger.info("🔄 Testing unified trader rollback...")

    try:
        from scripts.unified_live_trading import UnifiedLiveTrader

        # Create mock modern executor
        class MockModernExecutor:
            async def initialize(self):
                pass

            async def execute_transaction_with_bundles(self, tx_bytes):
                return {'success': True, 'signature': 'test_signature'}

        # Initialize trader with modern executor
        mock_executor = MockModernExecutor()
        trader = UnifiedLiveTrader(modern_executor=mock_executor)

        if trader.use_modern_components:
            logger.info("✅ Unified trader configured to use modern components")
        else:
            logger.error("❌ Unified trader not using modern components")
            return False

        # Initialize components
        success = await trader.initialize_components()
        if success:
            logger.info("✅ Unified trader components initialized successfully")
        else:
            logger.error("❌ Failed to initialize unified trader components")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Error testing unified trader: {e}")
        return False

async def test_environment_configuration():
    """Test that environment is properly configured for rollback."""
    logger.info("🔄 Testing environment configuration...")

    required_vars = [
        'WALLET_ADDRESS',
        'HELIUS_API_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.warning(f"⚠️ Missing environment variables: {missing_vars}")
        logger.info("ℹ️ Some tests may be limited without full environment setup")
    else:
        logger.info("✅ All required environment variables are set")

    return True

async def main():
    """Run all rollback tests."""
    logger.info("🔄 ROLLBACK VALIDATION TESTS")
    logger.info("="*60)

    tests = [
        ("Modern Components", test_modern_components),
        ("Transaction Executor Rollback", test_transaction_executor_rollback),
        ("Jupiter Client Rollback", test_jupiter_client_rollback),
        ("Unified Trader Rollback", test_unified_trader_rollback),
        ("Environment Configuration", test_environment_configuration),
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    logger.info("\n" + "="*60)
    logger.info("🔄 ROLLBACK VALIDATION SUMMARY")
    logger.info("="*60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All rollback tests passed! Signature verification fix is ready.")
        return 0
    else:
        logger.error("⚠️ Some rollback tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

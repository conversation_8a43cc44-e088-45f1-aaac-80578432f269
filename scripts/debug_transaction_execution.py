#!/usr/bin/env python3
"""
Debug Transaction Execution
Analyzes what's happening with Jupiter transaction execution.
"""

import asyncio
import logging
import os
import sys
import json
import base64
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_transaction_execution():
    """Debug what's happening with transaction execution."""
    logger.info("🔍 DEBUGGING TRANSACTION EXECUTION")
    logger.info("="*60)
    
    helius_api_key = os.getenv('HELIUS_API_KEY')
    wallet_address = os.getenv('WALLET_ADDRESS')
    
    endpoints = {
        'helius_mainnet': f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}",
        'jupiter_quote': "https://quote-api.jup.ag/v6",
        'jupiter_swap': "https://quote-api.jup.ag/v6/swap",
    }
    
    # Step 1: Check wallet balance and tokens
    logger.info("💰 Checking wallet balance and tokens...")
    await check_wallet_details(endpoints['helius_mainnet'], wallet_address)
    
    # Step 2: Get Jupiter quote
    logger.info("\n📋 Getting Jupiter quote...")
    quote = await get_jupiter_quote(endpoints)
    if not quote:
        return False
    
    # Step 3: Build transaction and analyze
    logger.info("\n🔨 Building and analyzing transaction...")
    transaction = await build_and_analyze_transaction(endpoints, quote, wallet_address)
    if not transaction:
        return False
    
    # Step 4: Test different submission methods
    logger.info("\n📤 Testing transaction submission...")
    await test_transaction_submission(endpoints['helius_mainnet'], transaction)
    
    return True

async def check_wallet_details(rpc_url, wallet_address):
    """Check wallet balance and token accounts."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Get SOL balance
            sol_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_address]
            }
            
            response = await client.post(rpc_url, json=sol_payload)
            result = response.json()
            
            if 'result' in result:
                sol_balance = result['result']['value'] / 1_000_000_000
                logger.info(f"✅ SOL balance: {sol_balance:.6f} SOL")
            
            # Get token accounts
            token_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenAccountsByOwner",
                "params": [
                    wallet_address,
                    {"programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"},
                    {"encoding": "jsonParsed"}
                ]
            }
            
            response = await client.post(rpc_url, json=token_payload)
            result = response.json()
            
            if 'result' in result:
                token_accounts = result['result']['value']
                logger.info(f"📊 Token accounts found: {len(token_accounts)}")
                
                # Look for USDC specifically
                usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
                for account in token_accounts:
                    parsed = account['account']['data']['parsed']
                    if parsed['info']['mint'] == usdc_mint:
                        usdc_balance = float(parsed['info']['tokenAmount']['uiAmount'] or 0)
                        logger.info(f"💵 USDC balance: ${usdc_balance:.6f}")
                        
                        if usdc_balance < 0.10:
                            logger.warning("⚠️ Insufficient USDC balance for trade!")
                            return False
                        break
                else:
                    logger.warning("⚠️ No USDC token account found!")
                    return False
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking wallet details: {e}")
        return False

async def get_jupiter_quote(endpoints):
    """Get Jupiter quote with detailed analysis."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{endpoints['jupiter_quote']}/quote"
            params = {
                'inputMint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                'outputMint': 'So11111111111111111111111111111111111111112',  # SOL
                'amount': '100000',  # $0.10 USDC
                'slippageBps': '50'
            }
            
            response = await client.get(url, params=params)
            response.raise_for_status()
            
            quote = response.json()
            if 'outAmount' in quote:
                input_amount = float(quote['inAmount']) / 1_000_000  # USDC has 6 decimals
                output_amount = float(quote['outAmount']) / 1_000_000_000  # SOL has 9 decimals
                
                logger.info(f"✅ Quote received:")
                logger.info(f"   Input: ${input_amount:.6f} USDC")
                logger.info(f"   Output: {output_amount:.6f} SOL")
                logger.info(f"   Route: {len(quote.get('routePlan', []))} steps")
                
                return quote
            else:
                logger.error(f"❌ Invalid quote: {quote}")
                return None
                
    except Exception as e:
        logger.error(f"❌ Error getting quote: {e}")
        return None

async def build_and_analyze_transaction(endpoints, quote, wallet_address):
    """Build transaction and analyze its structure."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            swap_data = {
                'quoteResponse': quote,
                'userPublicKey': wallet_address,
                'wrapAndUnwrapSol': True,
                'computeUnitPriceMicroLamports': 5000,
                'asLegacyTransaction': False
            }
            
            response = await client.post(endpoints['jupiter_swap'], json=swap_data)
            response.raise_for_status()
            
            result = response.json()
            if 'swapTransaction' in result:
                transaction = result['swapTransaction']
                
                # Analyze transaction
                tx_bytes = base64.b64decode(transaction)
                logger.info(f"✅ Transaction built:")
                logger.info(f"   Size: {len(tx_bytes)} bytes")
                logger.info(f"   Base64 length: {len(transaction)} characters")
                
                # Try to parse with solders
                try:
                    from solders.transaction import VersionedTransaction
                    tx = VersionedTransaction.from_bytes(tx_bytes)
                    
                    logger.info(f"   Signatures: {len(tx.signatures)}")
                    logger.info(f"   Message type: {type(tx.message).__name__}")
                    
                    # Check if pre-signed
                    signed_count = sum(1 for sig in tx.signatures if sig != bytes(64))
                    if signed_count > 0:
                        logger.warning(f"⚠️ Transaction has {signed_count} pre-existing signatures")
                    else:
                        logger.info("✅ Transaction is unsigned (ready for signing)")
                        
                except ImportError:
                    logger.warning("⚠️ Solders not available for detailed analysis")
                
                return transaction
            else:
                logger.error(f"❌ No transaction in response: {result}")
                return None
                
    except Exception as e:
        logger.error(f"❌ Error building transaction: {e}")
        return None

async def test_transaction_submission(rpc_url, transaction_b64):
    """Test different transaction submission methods."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # Method 1: Simulation first
            logger.info("🧪 Method 1: Transaction simulation...")
            sim_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "simulateTransaction",
                "params": [
                    transaction_b64,
                    {
                        "encoding": "base64",
                        "commitment": "processed",
                        "replaceRecentBlockhash": True
                    }
                ]
            }
            
            response = await client.post(rpc_url, json=sim_payload)
            result = response.json()
            
            if 'result' in result:
                sim_result = result['result']['value']
                if sim_result.get('err'):
                    logger.error(f"❌ Simulation failed: {sim_result['err']}")
                else:
                    logger.info("✅ Simulation successful")
                    logger.info(f"   Logs: {len(sim_result.get('logs', []))} entries")
                    logger.info(f"   Units consumed: {sim_result.get('unitsConsumed', 0)}")
            
            # Method 2: Check if DRY_RUN is set
            if os.getenv('DRY_RUN', '').lower() == 'true':
                logger.warning("⚠️ DRY_RUN mode is enabled - transactions will not be executed")
                return
            
            # Method 3: Actual submission
            logger.info("🚀 Method 2: Actual transaction submission...")
            tx_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    transaction_b64,
                    {
                        "encoding": "base64",
                        "skipPreflight": True,
                        "preflightCommitment": "processed",
                        "maxRetries": 0
                    }
                ]
            }
            
            response = await client.post(rpc_url, json=tx_payload)
            result = response.json()
            
            if 'result' in result:
                signature = result['result']
                logger.info(f"✅ Transaction submitted: {signature}")
                
                # Check if it's a real signature
                if signature == "1111111111111111111111111111111111111111111111111111111111111111":
                    logger.warning("⚠️ Received placeholder signature - transaction may not be real")
                else:
                    logger.info(f"🔗 View on Solscan: https://solscan.io/tx/{signature}")
                    
            elif 'error' in result:
                error_msg = result['error'].get('message', 'Unknown error')
                if 'signature verification failure' in error_msg.lower():
                    logger.error(f"🚨 SIGNATURE VERIFICATION FAILURE: {error_msg}")
                else:
                    logger.error(f"❌ Transaction error: {error_msg}")
            
    except Exception as e:
        logger.error(f"❌ Error testing submission: {e}")

async def main():
    """Main function."""
    success = await debug_transaction_execution()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

#!/usr/bin/env python3
"""
Jupiter Signature Issue Fix
Implements proper handling of Jupiter's pre-signed transactions.
"""

import asyncio
import logging
import os
import sys
import json
import base64
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JupiterSignatureFix:
    """Fix Jupiter signature verification issues."""
    
    def __init__(self):
        """Initialize the fix."""
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.keypair_path = os.getenv('KEYPAIR_PATH')
        
        self.endpoints = {
            'helius_mainnet': f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
            'jupiter_quote': "https://quote-api.jup.ag/v6",
            'jupiter_swap': "https://quote-api.jup.ag/v6/swap",
        }
    
    async def test_fixed_approach(self):
        """Test the fixed approach for Jupiter transactions."""
        logger.info("🔧 TESTING JUPITER SIGNATURE FIX")
        logger.info("="*50)
        
        # Method 1: Request unsigned transaction from Jupiter
        logger.info("🧪 Method 1: Request unsigned transaction from Jupiter...")
        success1 = await self._test_unsigned_transaction_approach()
        
        # Method 2: Use immediate execution with fresh blockhash
        logger.info("\n🧪 Method 2: Immediate execution approach...")
        success2 = await self._test_immediate_execution_approach()
        
        # Method 3: Use Jupiter's transaction with immediate submission
        logger.info("\n🧪 Method 3: Immediate submission approach...")
        success3 = await self._test_immediate_submission_approach()
        
        # Summary
        logger.info(f"\n📊 RESULTS SUMMARY:")
        logger.info(f"   Method 1 (Unsigned): {'✅ SUCCESS' if success1 else '❌ FAILED'}")
        logger.info(f"   Method 2 (Immediate): {'✅ SUCCESS' if success2 else '❌ FAILED'}")
        logger.info(f"   Method 3 (Fast Submit): {'✅ SUCCESS' if success3 else '❌ FAILED'}")
        
        return success1 or success2 or success3
    
    async def _test_unsigned_transaction_approach(self):
        """Test requesting unsigned transactions from Jupiter."""
        try:
            # Get quote
            quote = await self._get_jupiter_quote()
            if not quote:
                return False
            
            # Request unsigned transaction
            async with httpx.AsyncClient(timeout=30.0) as client:
                swap_data = {
                    'quoteResponse': quote,
                    'userPublicKey': self.wallet_address,
                    'wrapAndUnwrapSol': True,
                    'useSharedAccounts': True,
                    'computeUnitPriceMicroLamports': 5000,
                    'asLegacyTransaction': False,
                    'dynamicComputeUnitLimit': True,
                    'skipUserAccountsRpcCalls': True,
                    # KEY: Request unsigned transaction
                    'onlyQuote': False,
                    'restrictIntermediateTokens': False
                }
                
                response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                response.raise_for_status()
                
                result = response.json()
                if 'swapTransaction' in result:
                    transaction = result['swapTransaction']
                    
                    # Check if transaction is unsigned
                    tx_bytes = base64.b64decode(transaction)
                    
                    try:
                        from solders.transaction import VersionedTransaction
                        tx = VersionedTransaction.from_bytes(tx_bytes)
                        
                        # Check signatures
                        unsigned_sigs = sum(1 for sig in tx.signatures if sig == bytes(64))
                        if unsigned_sigs > 0:
                            logger.info(f"✅ Found {unsigned_sigs} unsigned signature slots")
                            
                            # Now we can sign with fresh blockhash
                            success = await self._sign_and_submit_transaction(tx)
                            return success
                        else:
                            logger.warning("⚠️ Transaction is still pre-signed")
                            return False
                            
                    except ImportError:
                        logger.warning("⚠️ Solders not available")
                        return False
                else:
                    logger.error("❌ No transaction in response")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error in unsigned transaction approach: {e}")
            return False
    
    async def _test_immediate_execution_approach(self):
        """Test immediate execution with fresh blockhash."""
        try:
            # Get quote and transaction
            quote = await self._get_jupiter_quote()
            if not quote:
                return False
            
            # Get fresh blockhash immediately before building transaction
            fresh_blockhash = await self._get_fresh_blockhash()
            if not fresh_blockhash:
                logger.error("❌ Could not get fresh blockhash")
                return False
            
            # Build transaction with fresh blockhash hint
            async with httpx.AsyncClient(timeout=10.0) as client:  # Shorter timeout for speed
                swap_data = {
                    'quoteResponse': quote,
                    'userPublicKey': self.wallet_address,
                    'wrapAndUnwrapSol': True,
                    'useSharedAccounts': True,
                    'computeUnitPriceMicroLamports': 5000,
                    'asLegacyTransaction': False,
                    'dynamicComputeUnitLimit': True,
                    'skipUserAccountsRpcCalls': True
                }
                
                response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                response.raise_for_status()
                
                result = response.json()
                if 'swapTransaction' in result:
                    transaction = result['swapTransaction']
                    
                    # Submit immediately (within seconds of creation)
                    success = await self._submit_transaction_immediately(transaction)
                    return success
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error in immediate execution approach: {e}")
            return False
    
    async def _test_immediate_submission_approach(self):
        """Test immediate submission approach."""
        try:
            # Get quote
            quote = await self._get_jupiter_quote()
            if not quote:
                return False
            
            # Build and submit in rapid succession
            start_time = datetime.now()
            
            async with httpx.AsyncClient(timeout=5.0) as client:  # Very short timeout
                # Build transaction
                swap_data = {
                    'quoteResponse': quote,
                    'userPublicKey': self.wallet_address,
                    'wrapAndUnwrapSol': True,
                    'computeUnitPriceMicroLamports': 5000,
                    'asLegacyTransaction': False
                }
                
                response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                response.raise_for_status()
                
                result = response.json()
                if 'swapTransaction' in result:
                    transaction = result['swapTransaction']
                    build_time = (datetime.now() - start_time).total_seconds()
                    
                    logger.info(f"⚡ Transaction built in {build_time:.3f} seconds")
                    
                    # Submit immediately
                    submit_start = datetime.now()
                    success = await self._submit_transaction_immediately(transaction)
                    submit_time = (datetime.now() - submit_start).total_seconds()
                    
                    total_time = (datetime.now() - start_time).total_seconds()
                    logger.info(f"⚡ Total time from quote to submission: {total_time:.3f} seconds")
                    
                    return success
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error in immediate submission approach: {e}")
            return False
    
    async def _get_jupiter_quote(self):
        """Get Jupiter quote."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                url = f"{self.endpoints['jupiter_quote']}/quote"
                params = {
                    'inputMint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                    'outputMint': 'So11111111111111111111111111111111111111112',  # SOL
                    'amount': '100000',  # $0.10 USDC (small test amount)
                    'slippageBps': '50'
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                quote = response.json()
                if 'outAmount' in quote:
                    logger.info(f"✅ Quote: {quote['outAmount']} output tokens")
                    return quote
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting quote: {e}")
            return None
    
    async def _get_fresh_blockhash(self):
        """Get fresh blockhash."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getLatestBlockhash",
                    "params": [{"commitment": "finalized"}]
                }
                
                response = await client.post(self.endpoints['helius_mainnet'], json=payload)
                response.raise_for_status()
                
                result = response.json()
                if 'result' in result:
                    blockhash = result['result']['value']['blockhash']
                    logger.info(f"✅ Fresh blockhash: {blockhash[:16]}...")
                    return blockhash
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting blockhash: {e}")
            return None
    
    async def _submit_transaction_immediately(self, transaction_b64):
        """Submit transaction immediately."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "sendTransaction",
                    "params": [
                        transaction_b64,
                        {
                            "encoding": "base64",
                            "skipPreflight": True,  # Skip preflight for speed
                            "preflightCommitment": "processed",
                            "maxRetries": 0
                        }
                    ]
                }
                
                response = await client.post(self.endpoints['helius_mainnet'], json=payload)
                result = response.json()
                
                if response.status_code == 200:
                    if 'result' in result:
                        logger.info(f"✅ Transaction submitted: {result['result']}")
                        return True
                    elif 'error' in result:
                        error_msg = result['error'].get('message', 'Unknown error')
                        if 'signature verification failure' in error_msg.lower():
                            logger.error(f"🚨 Still getting signature verification failure!")
                        else:
                            logger.warning(f"⚠️ Other error: {error_msg}")
                        return False
                else:
                    logger.error(f"❌ HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error submitting transaction: {e}")
            return False
    
    async def _sign_and_submit_transaction(self, tx):
        """Sign transaction with fresh blockhash and submit."""
        try:
            if not self.keypair_path or not os.path.exists(self.keypair_path):
                logger.error("❌ Keypair not available for signing")
                return False
            
            # Load keypair
            from solders.keypair import Keypair
            import json
            
            with open(self.keypair_path, 'r') as f:
                keypair_data = json.load(f)
            
            if len(keypair_data) == 64:
                keypair_bytes = bytes(keypair_data)
                keypair = Keypair.from_bytes(keypair_bytes)
            else:
                keypair_bytes = bytes(keypair_data + [0] * 32)
                keypair = Keypair.from_bytes(keypair_bytes[:32])
            
            # Get fresh blockhash
            fresh_blockhash = await self._get_fresh_blockhash()
            if not fresh_blockhash:
                return False
            
            # Update transaction with fresh blockhash
            from solders.hash import Hash
            tx.message.recent_blockhash = Hash.from_string(fresh_blockhash)
            
            # Sign transaction
            tx.sign([keypair])
            
            # Submit signed transaction
            signed_tx_bytes = bytes(tx)
            signed_tx_b64 = base64.b64encode(signed_tx_bytes).decode('utf-8')
            
            success = await self._submit_transaction_immediately(signed_tx_b64)
            return success
            
        except Exception as e:
            logger.error(f"❌ Error signing and submitting transaction: {e}")
            return False

async def main():
    """Main function."""
    fixer = JupiterSignatureFix()
    success = await fixer.test_fixed_approach()
    
    if success:
        logger.info("🎉 Found working solution for Jupiter signature verification!")
        return 0
    else:
        logger.error("❌ All approaches failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

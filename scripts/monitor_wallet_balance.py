#!/usr/bin/env python3
"""
🔍 CONTINUOUS WALLET BALANCE MONITOR
Monitors wallet balance changes in real-time to detect live trade execution
"""

import asyncio
import httpx
import json
from datetime import datetime
import time

async def get_wallet_balance():
    """Get current wallet balance."""
    try:
        helius_url = "https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e"
        wallet_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(helius_url, json={
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_address]
            })
            
            if response.status_code == 200:
                data = response.json()
                balance_lamports = data.get('result', {}).get('value', 0)
                balance_sol = balance_lamports / 1_000_000_000
                return balance_sol
    except Exception as e:
        print(f"❌ Error getting wallet balance: {e}")
        return None

async def monitor_wallet_balance():
    """Monitor wallet balance continuously."""
    print("🔍 STARTING CONTINUOUS WALLET BALANCE MONITOR")
    print("=" * 60)
    print("📍 Wallet: J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz")
    print("🎯 Monitoring for live trade execution...")
    print("=" * 60)
    
    # Get initial balance
    initial_balance = await get_wallet_balance()
    if initial_balance is None:
        print("❌ Failed to get initial balance")
        return
    
    print(f"💰 INITIAL BALANCE: {initial_balance:.9f} SOL")
    print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔄 Monitoring every 10 seconds...")
    print("-" * 60)
    
    previous_balance = initial_balance
    check_count = 0
    
    while True:
        try:
            await asyncio.sleep(10)  # Check every 10 seconds
            check_count += 1
            
            current_balance = await get_wallet_balance()
            if current_balance is None:
                print(f"⚠️ Check {check_count}: Failed to get balance")
                continue
            
            # Calculate change
            change = current_balance - previous_balance
            total_change = current_balance - initial_balance
            
            # Format timestamp
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            if abs(change) > 0.000001:  # Significant change detected
                print(f"🚨 BALANCE CHANGE DETECTED!")
                print(f"📅 Time: {timestamp}")
                print(f"💰 Previous: {previous_balance:.9f} SOL")
                print(f"💰 Current:  {current_balance:.9f} SOL")
                print(f"📊 Change:   {change:+.9f} SOL")
                print(f"📊 Total:    {total_change:+.9f} SOL")
                print("🎯 POSSIBLE TRADE EXECUTION!")
                print("-" * 60)
                
                # Update previous balance
                previous_balance = current_balance
            else:
                # No change - show periodic status
                if check_count % 6 == 0:  # Every minute
                    print(f"✅ {timestamp} | Balance: {current_balance:.9f} SOL | Total Change: {total_change:+.9f} SOL | Check #{check_count}")
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(monitor_wallet_balance())

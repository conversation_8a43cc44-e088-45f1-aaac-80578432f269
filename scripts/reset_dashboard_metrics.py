#!/usr/bin/env python3
"""
🔄 LIVE SESSION DASHBOARD RESET & SYNC SCRIPT
Resets dashboard metrics and syncs with current live trading session
"""

import json
import os
import asyncio
import httpx
from datetime import datetime
from pathlib import Path
import logging
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def get_current_wallet_balance():
    """Get current wallet balance for dashboard sync."""
    try:
        # Use Helius to get current balance
        helius_url = "https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e"
        wallet_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"

        async with httpx.AsyncClient() as client:
            response = await client.post(helius_url, json={
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_address]
            })

            if response.status_code == 200:
                data = response.json()
                balance_lamports = data.get('result', {}).get('value', 0)
                balance_sol = balance_lamports / 1_000_000_000
                return balance_sol
    except Exception as e:
        logger.error(f"Error getting wallet balance: {e}")
        return 1.690908  # Fallback to last known balance

def reset_live_session_metrics():
    """Reset all dashboard metrics for current live trading session."""

    # Current timestamp for reset
    reset_timestamp = datetime.now().isoformat()
    session_start = datetime.now()

    logger.info("🔄 RESETTING DASHBOARD FOR LIVE SESSION")
    logger.info(f"📅 Session Start: {session_start.strftime('%Y-%m-%d %H:%M:%S')}")

    # Files to reset for live session
    metrics_files = [
        "phase_4_deployment/output/latest_metrics.json",
        "phase_4_deployment/output/wallet_balance.json",
        "phase_4_deployment/output/trading_metrics.json",
        "phase_4_deployment/output/dashboard/performance_metrics.json",
        "phase_4_deployment/output/dashboard/latest_cycle.json",
        "output/live_production/dashboard/performance_metrics.json",
        "output/live_production/dashboard/latest_cycle.json",
        "output/enhanced_live_trading/latest_metrics.json"
    ]

    # Create fresh metrics for live session
    fresh_metrics = {
        "timestamp": reset_timestamp,
        "session_start": session_start.isoformat(),
        "session_duration_minutes": 0,
        "total_trades": 0,
        "successful_trades": 0,
        "failed_trades": 0,
        "total_pnl_sol": 0.0,
        "total_pnl_usd": 0.0,
        "win_rate": 0.0,
        "average_trade_size": 0.0,
        "largest_win": 0.0,
        "largest_loss": 0.0,
        "cycles_completed": 0,
        "signals_generated": 0,
        "signals_executed": 0,
        "execution_rate": 0.0,
        "last_trade_time": None,
        "status": "active",
        "trading_enabled": True
    }

    # Reset each metrics file
    reset_count = 0
    for file_path in metrics_files:
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Write fresh metrics
            with open(file_path, 'w') as f:
                json.dump(fresh_metrics, f, indent=2)

            logger.info(f"✅ Reset: {file_path}")
            reset_count += 1

        except Exception as e:
            logger.error(f"❌ Failed to reset {file_path}: {e}")

    logger.info(f"📊 Reset {reset_count}/{len(metrics_files)} metrics files")
    return fresh_metrics

async def sync_current_wallet_balance():
    """Sync current wallet balance to dashboard."""
    try:
        current_balance = await get_current_wallet_balance()

        wallet_data = {
            "timestamp": datetime.now().isoformat(),
            "wallet_address": "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz",
            "sol_balance": current_balance,
            "usd_value": current_balance * 100.0,  # Approximate SOL price
            "trading_balance": current_balance * 0.5,  # 50% for trading
            "reserve_balance": current_balance * 0.5,  # 50% reserve
            "last_updated": datetime.now().isoformat()
        }

        # Update wallet balance files
        wallet_files = [
            "phase_4_deployment/output/wallet_balance.json",
            "output/live_production/wallet_balance.json"
        ]

        for file_path in wallet_files:
            try:
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'w') as f:
                    json.dump(wallet_data, f, indent=2)
                logger.info(f"✅ Updated wallet balance: {file_path}")
            except Exception as e:
                logger.error(f"❌ Failed to update {file_path}: {e}")

        logger.info(f"💰 Current Balance: {current_balance:.6f} SOL")
        return wallet_data

    except Exception as e:
        logger.error(f"Error syncing wallet balance: {e}")
        return None

def create_live_session_summary():
    """Create live session summary for dashboard."""
    summary = {
        "session_info": {
            "start_time": datetime.now().isoformat(),
            "duration_target": "60 minutes",
            "trading_mode": "LIVE",
            "filters_removed": True,
            "position_sizing": "50% wallet strategy",
            "strategies_active": ["opportunistic_volatility_breakout", "momentum_sol_usdc", "wallet_momentum"]
        },
        "system_status": {
            "trading_enabled": True,
            "dry_run": False,
            "paper_trading": False,
            "api_status": {
                "birdeye": "rate_limited_fallback_active",
                "helius": "operational",
                "jito": "operational"
            },
            "circuit_breaker": "active",
            "fallback_system": "ready"
        },
        "metrics_reset": {
            "reset_time": datetime.now().isoformat(),
            "previous_data_cleared": True,
            "fresh_tracking_enabled": True
        }
    }

    # Save session summary
    summary_file = "phase_4_deployment/output/live_session_summary.json"
    try:
        os.makedirs(os.path.dirname(summary_file), exist_ok=True)
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"✅ Created session summary: {summary_file}")
    except Exception as e:
        logger.error(f"❌ Failed to create session summary: {e}")

    return summary

async def main():
    """Main reset and sync function."""
    logger.info("🚀 LIVE SESSION DASHBOARD RESET & SYNC")
    logger.info("=" * 60)

    # Step 1: Reset metrics for fresh session
    logger.info("\n📊 Step 1: Resetting dashboard metrics...")
    fresh_metrics = reset_live_session_metrics()

    # Step 2: Sync current wallet balance
    logger.info("\n💰 Step 2: Syncing current wallet balance...")
    wallet_data = await sync_current_wallet_balance()

    # Step 3: Create session summary
    logger.info("\n📋 Step 3: Creating live session summary...")
    session_summary = create_live_session_summary()

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("✅ DASHBOARD RESET COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📅 Session Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    if wallet_data:
        logger.info(f"💰 Current Balance: {wallet_data['sol_balance']:.6f} SOL")
    logger.info("📊 All metrics reset to 0 for fresh tracking")
    logger.info("🔄 Dashboard will now show current session data")
    logger.info("\n🎯 Ready for live trading session monitoring!")

if __name__ == "__main__":
    asyncio.run(main())


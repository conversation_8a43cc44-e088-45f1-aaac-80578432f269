#!/usr/bin/env python3
"""
Live Test with Balance Proof
Executes a real trade to prove the signature verification fix works.
"""

import asyncio
import logging
import os
import sys
import json
import base64
import time
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveBalanceProofTest:
    """Live test with actual balance changes as proof."""
    
    def __init__(self):
        """Initialize the test."""
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.keypair_path = os.getenv('KEYPAIR_PATH')
        
        self.endpoints = {
            'helius_mainnet': f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
            'jupiter_quote': "https://quote-api.jup.ag/v6",
            'jupiter_swap': "https://quote-api.jup.ag/v6/swap",
        }
    
    async def run_live_test(self):
        """Run live test with balance proof."""
        logger.info("🔥 LIVE TEST WITH BALANCE PROOF")
        logger.info("="*60)
        logger.warning("⚠️ THIS WILL EXECUTE A REAL TRADE ON MAINNET!")
        
        # Get initial balance
        initial_balance = await self._get_wallet_balance()
        if initial_balance is None:
            logger.error("❌ Cannot get initial wallet balance")
            return False
        
        logger.info(f"💰 Initial wallet balance: {initial_balance:.6f} SOL")
        
        # Execute real trade
        success = await self._execute_real_trade()
        
        if success:
            # Wait for confirmation
            logger.info("⏳ Waiting 10 seconds for transaction confirmation...")
            await asyncio.sleep(10)
            
            # Get final balance
            final_balance = await self._get_wallet_balance()
            if final_balance is not None:
                balance_change = final_balance - initial_balance
                logger.info(f"💰 Final wallet balance: {final_balance:.6f} SOL")
                logger.info(f"📊 Balance change: {balance_change:+.6f} SOL")
                
                if abs(balance_change) > 0.000001:  # More than 1 microSOL
                    logger.info("🎉 WALLET BALANCE CHANGED - PROOF OF SUCCESSFUL TRADE!")
                    logger.info("✅ SIGNATURE VERIFICATION FIX CONFIRMED WORKING!")
                    return True
                else:
                    logger.warning("⚠️ No significant balance change detected")
                    return False
            else:
                logger.error("❌ Could not get final balance")
                return False
        else:
            return False
    
    async def _execute_real_trade(self):
        """Execute a real trade using the immediate submission fix."""
        try:
            logger.info("🔄 Executing real trade with immediate submission...")
            
            # Step 1: Get quote for small trade ($0.10 USDC -> SOL)
            quote = await self._get_jupiter_quote()
            if not quote:
                return False
            
            # Step 2: Build transaction immediately
            transaction = await self._build_jupiter_transaction(quote)
            if not transaction:
                return False
            
            # Step 3: Submit immediately (within 1-2 seconds)
            success = await self._submit_transaction_immediately(transaction)
            return success
            
        except Exception as e:
            logger.error(f"❌ Error executing real trade: {e}")
            return False
    
    async def _get_jupiter_quote(self):
        """Get Jupiter quote for small trade."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                url = f"{self.endpoints['jupiter_quote']}/quote"
                params = {
                    'inputMint': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                    'outputMint': 'So11111111111111111111111111111111111111112',  # SOL
                    'amount': '100000',  # $0.10 USDC
                    'slippageBps': '50'
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                quote = response.json()
                if 'outAmount' in quote:
                    output_sol = int(quote['outAmount']) / 1_000_000_000
                    logger.info(f"✅ Quote: $0.10 USDC → {output_sol:.6f} SOL")
                    return quote
                else:
                    logger.error(f"❌ Invalid quote: {quote}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting quote: {e}")
            return None
    
    async def _build_jupiter_transaction(self, quote):
        """Build Jupiter transaction."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                swap_data = {
                    'quoteResponse': quote,
                    'userPublicKey': self.wallet_address,
                    'wrapAndUnwrapSol': True,
                    'computeUnitPriceMicroLamports': 5000,
                    'asLegacyTransaction': False
                }
                
                response = await client.post(self.endpoints['jupiter_swap'], json=swap_data)
                response.raise_for_status()
                
                result = response.json()
                if 'swapTransaction' in result:
                    transaction = result['swapTransaction']
                    logger.info(f"✅ Transaction built: {len(transaction)} characters")
                    return transaction
                else:
                    logger.error(f"❌ No transaction in response: {result}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error building transaction: {e}")
            return None
    
    async def _submit_transaction_immediately(self, transaction_b64):
        """Submit transaction immediately using the signature verification fix."""
        try:
            logger.info("⚡ Submitting transaction immediately...")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "sendTransaction",
                    "params": [
                        transaction_b64,
                        {
                            "encoding": "base64",
                            "skipPreflight": True,  # FIXED: Skip for immediate submission
                            "preflightCommitment": "processed",  # FIXED: Use processed for speed
                            "maxRetries": 0
                        }
                    ]
                }
                
                response = await client.post(self.endpoints['helius_mainnet'], json=payload)
                result = response.json()
                
                if response.status_code == 200:
                    if 'result' in result:
                        signature = result['result']
                        logger.info(f"🎉 REAL TRADE EXECUTED SUCCESSFULLY!")
                        logger.info(f"📝 Transaction signature: {signature}")
                        logger.info(f"🔗 View on Solscan: https://solscan.io/tx/{signature}")
                        return True
                    elif 'error' in result:
                        error_msg = result['error'].get('message', 'Unknown error')
                        if 'signature verification failure' in error_msg.lower():
                            logger.error(f"🚨 SIGNATURE VERIFICATION FAILURE!")
                            logger.error(f"   Error: {error_msg}")
                        else:
                            logger.error(f"❌ Transaction failed: {error_msg}")
                        return False
                else:
                    logger.error(f"❌ HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error submitting transaction: {e}")
            return False
    
    async def _get_wallet_balance(self):
        """Get current wallet balance."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBalance",
                    "params": [self.wallet_address]
                }
                
                response = await client.post(self.endpoints['helius_mainnet'], json=payload)
                result = response.json()
                
                if 'result' in result:
                    balance_lamports = result['result']['value']
                    balance_sol = balance_lamports / 1_000_000_000
                    return balance_sol
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting balance: {e}")
            return None

async def main():
    """Main function."""
    logger.warning("🚨 LIVE TRADING TEST - REAL MONEY WILL BE USED!")
    logger.warning("🚨 This will execute a $0.10 USDC trade on Solana mainnet")
    
    response = input("\nAre you absolutely sure you want to proceed with LIVE trading? (type 'YES' to confirm): ")
    if response != 'YES':
        logger.info("❌ Test cancelled by user")
        return 1
    
    logger.info("🚀 Starting live test...")
    
    tester = LiveBalanceProofTest()
    success = await tester.run_live_test()
    
    if success:
        logger.info("\n" + "="*60)
        logger.info("🎉 LIVE TEST SUCCESSFUL!")
        logger.info("✅ Signature verification fix confirmed working with real trades!")
        logger.info("✅ Ready to update unified live trading system!")
        logger.info("="*60)
        return 0
    else:
        logger.error("\n" + "="*60)
        logger.error("❌ LIVE TEST FAILED!")
        logger.error("❌ Signature verification fix needs more work")
        logger.error("="*60)
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

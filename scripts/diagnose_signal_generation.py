#!/usr/bin/env python3
"""
Signal Generation Diagnostic Script
Diagnoses the USDC-USDC signal generation issue
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from phase_4_deployment.data_router.birdeye_scanner import BirdeyeScanner
from phase_4_deployment.apis.birdeye_client import BirdeyeClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def diagnose_birdeye_opportunities():
    """Diagnose what opportunities Birdeye scanner is returning."""
    logger.info("🔍 DIAGNOSING BIRDEYE SCANNER OPPORTUNITIES")

    try:
        # Initialize Birdeye client and scanner
        client = BirdeyeClient()
        scanner = BirdeyeScanner(client)

        # Scan for opportunities
        opportunities = await scanner.scan_for_opportunities(limit=5)

        logger.info(f"📊 Found {len(opportunities)} opportunities:")

        for i, opp in enumerate(opportunities, 1):
            symbol = opp.get('symbol', 'UNKNOWN')
            token_address = opp.get('token_address', 'UNKNOWN')
            price = opp.get('price', 0)
            score = opp.get('score', 0)

            # This is the problematic line - creating market pairs
            market_pair = f"{symbol}-USDC"

            logger.info(f"  {i}. Symbol: {symbol}")
            logger.info(f"     Token Address: {token_address}")
            logger.info(f"     Price: ${price}")
            logger.info(f"     Score: {score}")
            logger.info(f"     Generated Market: {market_pair}")
            logger.info(f"     ❌ PROBLEM: {market_pair} {'is INVALID' if symbol == 'USDC' else 'is valid'}")
            logger.info("")

        return opportunities

    except Exception as e:
        logger.error(f"❌ Error diagnosing Birdeye opportunities: {e}")
        return []

async def diagnose_signal_generation():
    """Diagnose the complete signal generation flow."""
    logger.info("🔍 DIAGNOSING COMPLETE SIGNAL GENERATION FLOW")

    # Get opportunities from Birdeye
    opportunities = await diagnose_birdeye_opportunities()

    if not opportunities:
        logger.error("❌ No opportunities found - cannot diagnose signal generation")
        return

    logger.info("🎯 SIGNAL GENERATION ANALYSIS:")

    # Simulate signal generation logic from unified_live_trading.py
    for opp in opportunities[:3]:  # Top 3 opportunities
        symbol = opp.get('symbol', 'UNKNOWN')

        # This is the exact logic from line 863 and 927
        market_pair = f"{symbol}-USDC"

        logger.info(f"📊 Opportunity: {symbol}")
        logger.info(f"   Generated Market: {market_pair}")

        if symbol == 'USDC':
            logger.error(f"   ❌ INVALID PAIR: {market_pair} (same token swap)")
            logger.info(f"   🔧 SHOULD BE: SOL-USDC or other valid pair")
        else:
            logger.info(f"   ✅ VALID PAIR: {market_pair}")

        logger.info("")

def propose_fix():
    """Propose the fix for the signal generation issue."""
    logger.info("🔧 PROPOSED FIX:")
    logger.info("")
    logger.info("PROBLEM:")
    logger.info("  Line 863 & 927: 'market': f\"{opp.get('symbol', 'UNKNOWN')}-USDC\"")
    logger.info("  When symbol='USDC', this creates 'USDC-USDC' (invalid)")
    logger.info("")
    logger.info("SOLUTION:")
    logger.info("  1. Filter out USDC opportunities")
    logger.info("  2. OR map USDC to SOL-USDC")
    logger.info("  3. OR use token address mapping to create valid pairs")
    logger.info("")
    logger.info("RECOMMENDED FIX:")
    logger.info("  Replace symbol-based market creation with smart mapping:")
    logger.info("  - If symbol == 'USDC': use 'SOL-USDC'")
    logger.info("  - If symbol == 'SOL': use 'SOL-USDC'")
    logger.info("  - Otherwise: use '{symbol}-USDC'")

async def main():
    """Main diagnostic function."""
    logger.info("🚀 SIGNAL GENERATION DIAGNOSTIC TOOL")
    logger.info("="*60)

    # Run diagnostics
    await diagnose_signal_generation()

    # Propose fix
    propose_fix()

    logger.info("="*60)
    logger.info("✅ Diagnostic complete")

if __name__ == "__main__":
    asyncio.run(main())

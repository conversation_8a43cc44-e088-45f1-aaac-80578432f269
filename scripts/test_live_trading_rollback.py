#!/usr/bin/env python3
"""
Test Live Trading with Rollback Implementation
Tests the signature verification fix with actual wallet balance monitoring.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def get_wallet_balance():
    """Get current wallet balance for monitoring."""
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        
        wallet_address = os.getenv('WALLET_ADDRESS')
        helius_api_key = os.getenv('HELIUS_API_KEY')
        
        if not wallet_address or not helius_api_key:
            logger.error("❌ Missing WALLET_ADDRESS or HELIUS_API_KEY")
            return None
        
        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)
        
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            return balance_data['balance_sol']
        else:
            logger.warning(f"⚠️ Could not get wallet balance: {balance_data}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error getting wallet balance: {e}")
        return None

async def initialize_modern_components():
    """Initialize modern components for testing."""
    try:
        logger.info("🔧 Initializing modern components...")
        
        # Initialize modern transaction executor
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        modern_executor = ModernTransactionExecutor()
        await modern_executor.initialize()
        logger.info("✅ Modern transaction executor initialized")
        
        # Initialize modern Jupiter client
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient
        jupiter_client = ModernJupiterClient()
        logger.info("✅ Modern Jupiter client initialized")
        
        return modern_executor, jupiter_client
        
    except Exception as e:
        logger.error(f"❌ Error initializing modern components: {e}")
        return None, None

async def test_live_trading_session(duration_minutes=5):
    """Test live trading session with balance monitoring."""
    logger.info("🚀 TESTING LIVE TRADING WITH ROLLBACK IMPLEMENTATION")
    logger.info("="*70)
    
    # Check initial wallet balance
    logger.info("💰 Checking initial wallet balance...")
    initial_balance = await get_wallet_balance()
    if initial_balance is None:
        logger.error("❌ Cannot proceed without wallet balance")
        return False
    
    logger.info(f"💰 Initial wallet balance: {initial_balance:.6f} SOL")
    
    # Initialize modern components
    modern_executor, jupiter_client = await initialize_modern_components()
    if not modern_executor or not jupiter_client:
        logger.error("❌ Failed to initialize modern components")
        return False
    
    # Initialize unified live trader with modern components
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        trader = UnifiedLiveTrader(
            modern_executor=modern_executor,
            jupiter_client=jupiter_client
        )
        
        logger.info("✅ Unified live trader initialized with modern components")
        
        # Initialize trader components
        success = await trader.initialize_components()
        if not success:
            logger.error("❌ Failed to initialize trader components")
            return False
        
        logger.info("✅ Trader components initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Error initializing trader: {e}")
        return False
    
    # Run live trading session
    try:
        logger.info(f"🔄 Starting {duration_minutes}-minute live trading session...")
        logger.info("🔍 Monitoring for:")
        logger.info("   - Signature verification failures")
        logger.info("   - Transaction execution success")
        logger.info("   - Wallet balance changes")
        logger.info("   - Jito Bundle execution")
        
        start_time = datetime.now()
        
        # Run the trading session
        success = await trader.run_live_trading(duration_minutes)
        
        end_time = datetime.now()
        session_duration = (end_time - start_time).total_seconds() / 60
        
        logger.info(f"⏱️ Trading session completed in {session_duration:.2f} minutes")
        
        # Check final wallet balance
        logger.info("💰 Checking final wallet balance...")
        final_balance = await get_wallet_balance()
        
        if final_balance is not None:
            balance_change = final_balance - initial_balance
            logger.info(f"💰 Final wallet balance: {final_balance:.6f} SOL")
            logger.info(f"📊 Balance change: {balance_change:+.6f} SOL")
            
            if abs(balance_change) > 0.000001:  # More than 1 microSOL change
                logger.info("✅ WALLET BALANCE CHANGED - TRADES EXECUTED SUCCESSFULLY!")
                logger.info("🎉 Rollback implementation working correctly")
            else:
                logger.warning("⚠️ No wallet balance change detected")
                logger.info("ℹ️ This could mean:")
                logger.info("   - No trading signals generated")
                logger.info("   - Dry run mode enabled")
                logger.info("   - Trades failed to execute")
        else:
            logger.error("❌ Could not get final wallet balance")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error during trading session: {e}")
        return False
    
    finally:
        # Clean up
        try:
            await modern_executor.close()
            await jupiter_client.close()
            logger.info("✅ Modern components closed")
        except:
            pass

async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Live Trading with Rollback")
    parser.add_argument("--duration", type=float, default=5.0, help="Trading duration in minutes")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode (no real trades)")
    
    args = parser.parse_args()
    
    # Set environment for testing
    if args.dry_run:
        os.environ['DRY_RUN'] = 'true'
        logger.info("🧪 Running in DRY RUN mode - no real trades will be executed")
    else:
        os.environ['DRY_RUN'] = 'false'
        logger.warning("⚠️ Running in LIVE mode - REAL TRADES will be executed")
        
        # Confirm with user
        response = input("Are you sure you want to execute REAL trades? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("❌ Test cancelled by user")
            return 1
    
    # Ensure trading is enabled
    os.environ['TRADING_ENABLED'] = 'true'
    
    # Run the test
    success = await test_live_trading_session(args.duration)
    
    if success:
        logger.info("✅ Live trading test completed successfully")
        logger.info("🔄 Rollback implementation validated")
        return 0
    else:
        logger.error("❌ Live trading test failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

#!/usr/bin/env python3
"""
Simple Balance Check
Uses the same method as the trading system to check wallet balance.
"""

import asyncio
import httpx
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def check_balance_simple():
    """Check wallet balance using direct RPC call."""
    print("💰 RWA Trading System - Simple Balance Check")
    print("="*50)
    
    wallet_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
    helius_api_key = os.getenv('HELIUS_API_KEY', 'dda9f776-9a40-447d-9ca4-22a27c21169e')
    rpc_url = f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}"
    
    print(f"📍 Wallet: {wallet_address}")
    print()
    
    try:
        async with httpx.AsyncClient() as client:
            # Get SOL balance
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_address]
            }
            
            response = await client.post(rpc_url, json=payload)
            result = response.json()
            
            if 'result' in result:
                lamports = result['result']['value']
                sol_balance = lamports / 1e9
                print(f"💎 SOL Balance: {sol_balance:.9f} SOL")
                print(f"💵 SOL Value: ${sol_balance * 174.5:.2f} USD (approx)")
            else:
                print(f"❌ Error getting SOL balance: {result}")
                return False
            
            # Get token accounts for USDC
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenAccountsByOwner",
                "params": [
                    wallet_address,
                    {"mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},
                    {"encoding": "jsonParsed"}
                ]
            }
            
            response = await client.post(rpc_url, json=payload)
            result = response.json()
            
            if 'result' in result:
                accounts = result['result']['value']
                total_usdc = 0
                
                for account in accounts:
                    token_amount = account['account']['data']['parsed']['info']['tokenAmount']
                    amount = float(token_amount['uiAmount'] or 0)
                    total_usdc += amount
                
                print(f"💵 USDC Balance: {total_usdc:.6f} USDC")
            else:
                print(f"⚠️ Could not get USDC balance: {result}")
            
            print()
            print("✅ Balance check completed successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error checking balance: {e}")
        return False

async def main():
    """Main function."""
    success = await check_balance_simple()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

#!/usr/bin/env python3
"""
RWA Trading System Live Dashboard
Real-time monitoring dashboard for the RWA Trading System with Jito Bundle execution and Orca DEX integration.
"""

import os
import sys
import json
import time
import logging
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import glob
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set page config
st.set_page_config(
    page_title="RWA Trading System Live Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

class LiveTradingDashboard:
    """Live trading dashboard for 48-hour session monitoring."""

    def __init__(self):
        self.data_dir = Path("phase_4_deployment/output")
        self.logs_dir = Path("logs")
        self.refresh_interval = 30  # seconds

    def load_trading_metrics(self) -> Dict[str, Any]:
        """Load trading metrics from files."""
        try:
            metrics_file = self.data_dir / "trading_metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading trading metrics: {e}")

        return {
            'total_trades': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'last_update': datetime.now().isoformat()
        }

    def load_transaction_history(self) -> List[Dict[str, Any]]:
        """Load transaction history."""
        try:
            tx_file = self.data_dir / "tx_history.json"
            if tx_file.exists():
                with open(tx_file, 'r') as f:
                    data = json.load(f)
                    return data.get('transactions', [])
        except Exception as e:
            logger.error(f"Error loading transaction history: {e}")

        return []

    def load_session_data(self) -> Dict[str, Any]:
        """Load 48-hour session data."""
        try:
            session_file = self.logs_dir / "48_hour_session_data.json"
            if session_file.exists():
                with open(session_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading session data: {e}")

        return {
            'start_time': None,
            'trades_executed': 0,
            'total_pnl': 0.0,
            'alerts_sent': 0,
            'errors_encountered': 0,
            'uptime_percentage': 0.0
        }

    def load_wallet_balance(self) -> Dict[str, Any]:
        """Load current wallet balance."""
        try:
            wallet_file = self.data_dir / "wallet_balance.json"
            if wallet_file.exists():
                with open(wallet_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading wallet balance: {e}")

        return {
            'balance_sol': 0.0,
            'balance_usd': 0.0,
            'last_update': datetime.now().isoformat()
        }

    def render_header(self):
        """Render dashboard header."""
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            st.image("https://solana.com/_next/static/media/logotype.e4df684f.svg", width=120)

        with col2:
            st.title("🚀 RWA Trading System Live Dashboard")
            st.caption("Phase 1-3 Optimizations • Dynamic Position Sizing • Multi-Strategy Adaptive Weighting")

        with col3:
            st.write(f"**Last Updated:** {datetime.now().strftime('%H:%M:%S')}")
            if st.button("🔄 Refresh"):
                st.rerun()

    def render_session_overview(self, session_data: Dict[str, Any]):
        """Render session overview metrics."""
        st.header("📊 Session Overview")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if session_data.get('start_time'):
                start_time = datetime.fromisoformat(session_data['start_time'])
                elapsed = datetime.now() - start_time
                elapsed_hours = elapsed.total_seconds() / 3600
                remaining_hours = max(0, 48 - elapsed_hours)

                st.metric(
                    "⏱️ Session Time",
                    f"{elapsed_hours:.1f}h / 48h",
                    f"{remaining_hours:.1f}h remaining"
                )
            else:
                st.metric("⏱️ Session Time", "Not Started")

        with col2:
            st.metric(
                "📈 Trades Executed",
                session_data.get('trades_executed', 0)
            )

        with col3:
            pnl = session_data.get('total_pnl', 0.0)
            pnl_color = "normal" if pnl >= 0 else "inverse"
            st.metric(
                "💰 Total PnL",
                f"${pnl:.2f}",
                delta=f"${pnl:.2f}",
                delta_color=pnl_color
            )

        with col4:
            uptime = session_data.get('uptime_percentage', 0.0)
            st.metric(
                "🔧 System Uptime",
                f"{uptime:.1f}%"
            )

    def render_trading_metrics(self, metrics: Dict[str, Any]):
        """Render trading performance metrics."""
        st.header("📈 Trading Performance")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "🎯 Total Trades",
                metrics.get('total_trades', 0)
            )

        with col2:
            successful = metrics.get('successful_trades', 0)
            total = metrics.get('total_trades', 1)
            win_rate = (successful / total) * 100 if total > 0 else 0
            st.metric(
                "✅ Win Rate",
                f"{win_rate:.1f}%"
            )

        with col3:
            pnl = metrics.get('total_pnl', 0.0)
            pnl_color = "normal" if pnl >= 0 else "inverse"
            st.metric(
                "💵 Total PnL",
                f"${pnl:.2f}",
                delta=f"${pnl:.2f}",
                delta_color=pnl_color
            )

        with col4:
            avg_pnl = pnl / total if total > 0 else 0
            st.metric(
                "📊 Avg PnL/Trade",
                f"${avg_pnl:.2f}"
            )

    def render_transaction_history(self, transactions: List[Dict[str, Any]]):
        """Render recent transaction history."""
        st.header("📋 Recent Transactions")

        if not transactions:
            st.info("No transactions found")
            return

        # Convert to DataFrame
        df = pd.DataFrame(transactions[-20:])  # Last 20 transactions

        if 'timestamp' in df.columns:
            df['time'] = pd.to_datetime(df['timestamp'], unit='s').dt.strftime('%H:%M:%S')

        # Display table
        display_cols = ['time', 'signature', 'status']
        if all(col in df.columns for col in display_cols):
            st.dataframe(
                df[display_cols].rename(columns={
                    'time': 'Time',
                    'signature': 'Transaction ID',
                    'status': 'Status'
                }),
                use_container_width=True
            )
        else:
            st.dataframe(df, use_container_width=True)

        # Transaction status chart
        if 'status' in df.columns:
            status_counts = df['status'].value_counts()
            fig = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Transaction Status Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)

    def render_wallet_info(self, wallet_data: Dict[str, Any]):
        """Render wallet information."""
        st.header("💰 Wallet Status")

        col1, col2 = st.columns(2)

        with col1:
            balance_sol = wallet_data.get('balance_sol', 0.0)
            st.metric(
                "SOL Balance",
                f"{balance_sol:.4f} SOL"
            )

        with col2:
            balance_usd = wallet_data.get('balance_usd', 0.0)
            st.metric(
                "USD Value",
                f"${balance_usd:.2f}"
            )

        # Wallet address
        wallet_address = os.getenv('WALLET_ADDRESS', 'Not configured')
        if wallet_address != 'Not configured':
            st.code(f"Wallet: {wallet_address}")

    def render_system_status(self):
        """Render system status indicators."""
        st.header("🔧 System Status")

        col1, col2, col3 = st.columns(3)

        with col1:
            # Check if signature verification fixes are enabled
            fixes_enabled = os.getenv('JITO_SIGNATURE_FIXES_ENABLED', 'false').lower() == 'true'
            status = "✅ ENABLED" if fixes_enabled else "❌ DISABLED"
            st.metric("Jito Signature Fixes", status)

        with col2:
            # Check if trading is active (simplified check)
            trading_active = True  # Would check actual process status
            status = "🟢 ACTIVE" if trading_active else "🔴 INACTIVE"
            st.metric("Trading System", status)

        with col3:
            # Check dashboard status
            st.metric("Dashboard", "🟢 ONLINE")

    def render_alerts_log(self):
        """Render recent alerts and notifications."""
        st.header("🚨 Recent Alerts")

        try:
            alert_file = self.logs_dir / "alert_history.json"
            if alert_file.exists():
                with open(alert_file, 'r') as f:
                    alerts = json.load(f)

                if alerts:
                    # Show last 10 alerts
                    recent_alerts = alerts[-10:]
                    for alert in reversed(recent_alerts):
                        timestamp = alert.get('timestamp', 'Unknown')
                        message = alert.get('message', 'No message')
                        level = alert.get('level', 'INFO')

                        if level == 'ERROR':
                            st.error(f"**{timestamp}**: {message}")
                        elif level == 'WARNING':
                            st.warning(f"**{timestamp}**: {message}")
                        else:
                            st.info(f"**{timestamp}**: {message}")
                else:
                    st.info("No alerts found")
            else:
                st.info("No alert history available")
        except Exception as e:
            st.error(f"Error loading alerts: {e}")

    def run_dashboard(self):
        """Run the main dashboard."""
        # Auto-refresh setup
        placeholder = st.empty()

        with placeholder.container():
            # Render header
            self.render_header()

            # Load data
            session_data = self.load_session_data()
            trading_metrics = self.load_trading_metrics()
            transactions = self.load_transaction_history()
            wallet_data = self.load_wallet_balance()

            # Create enhanced tabs
            tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
                "📊 Overview",
                "🎯 Strategy Performance",
                "📈 Market Intelligence",
                "⚙️ System Health",
                "🔍 Signal Analysis",
                "💰 Risk Management",
                "🚨 Alerts"
            ])

            with tab1:
                self.render_session_overview(session_data)
                self.render_system_status()

            with tab2:
                self.render_trading_metrics(trading_metrics)

            with tab3:
                self.render_transaction_history(transactions)

            with tab4:
                self.render_wallet_info(wallet_data)

            with tab5:
                self.render_alerts_log()

        # Auto-refresh
        time.sleep(self.refresh_interval)
        st.rerun()

def main():
    """Main function to run the dashboard."""
    dashboard = LiveTradingDashboard()
    dashboard.run_dashboard()

if __name__ == "__main__":
    main()

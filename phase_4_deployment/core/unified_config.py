#!/usr/bin/env python3
"""
Unified Configuration System for Synergy7
Centralizes all configuration sources and eliminates hardcoded parameters.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from pathlib import Path

logger = logging.getLogger(__name__)

class UnifiedConfig:
    """Unified configuration manager that consolidates all config sources."""

    def __init__(self, config_path: str = "config.yaml", env_file: str = ".env"):
        """Initialize unified configuration."""
        self.config_path = config_path
        self.env_file = env_file
        self.config = {}
        self._loaded = False

    def load(self) -> Dict[str, Any]:
        """Load and merge all configuration sources."""
        if self._loaded:
            return self.config

        try:
            # Step 1: Load environment variables
            self._load_environment()

            # Step 2: Load base configuration
            self._load_base_config()

            # Step 3: Apply environment overrides
            self._apply_environment_overrides()

            # Step 4: Set computed values
            self._set_computed_values()

            # Step 5: Validate configuration
            self._validate_config()

            self._loaded = True
            logger.info("✅ Unified configuration loaded successfully")
            return self.config

        except Exception as e:
            logger.error(f"❌ Error loading unified configuration: {e}")
            raise

    def _load_environment(self):
        """Load environment variables from .env file."""
        try:
            if os.path.exists(self.env_file):
                load_dotenv(self.env_file, override=True)
                logger.info(f"✅ Environment variables loaded from {self.env_file}")
            else:
                logger.warning(f"⚠️ Environment file not found: {self.env_file}")
        except Exception as e:
            logger.error(f"❌ Error loading environment: {e}")
            raise

    def _load_base_config(self):
        """Load base configuration from YAML file."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    content = f.read()
                    # Replace environment variable placeholders
                    content = self._resolve_env_placeholders(content)
                    self.config = yaml.safe_load(content) or {}
                logger.info(f"✅ Base configuration loaded from {self.config_path}")
            else:
                logger.warning(f"⚠️ Config file not found: {self.config_path}, using defaults")
                self.config = self._get_default_config()
        except Exception as e:
            logger.error(f"❌ Error loading base config: {e}")
            raise

    def _resolve_env_placeholders(self, content: str) -> str:
        """Resolve ${VAR} and ${VAR:-default} placeholders in config content."""
        import re

        def replace_env_var(match):
            full_match = match.group(1)
            if ':-' in full_match:
                # Handle ${VAR:-default} syntax
                var_name, default_value = full_match.split(':-', 1)
                return os.getenv(var_name, default_value)
            else:
                # Handle ${VAR} syntax
                var_name = full_match
                return os.getenv(var_name, f"${{{var_name}}}")  # Keep placeholder if not found

        return re.sub(r'\$\{([^}]+)\}', replace_env_var, content)

    def _apply_environment_overrides(self):
        """Apply environment variable overrides to configuration."""
        # Define environment variable mappings
        env_mappings = {
            # RPC Configuration - 🔧 QUICKNODE INTEGRATION
            'QUICKNODE_RPC_URL': 'rpc.primary_url',  # 🔧 QuickNode is now primary
            'HELIUS_RPC_URL': 'rpc.fallback_url',    # 🔧 Helius is now fallback
            'JITO_RPC_URL': 'rpc.jito_url',
            'QUICKNODE_BUNDLE_URL': 'rpc.quicknode_bundle_url',
            'HELIUS_API_KEY': 'apis.helius.api_key',
            'QUICKNODE_API_KEY': 'apis.quicknode.api_key',

            # QuickNode Bundle Configuration - 🔧 PHASE 1 COMPLETE
            'QUICKNODE_BUNDLES_ENABLED': 'quicknode_bundles.enabled',
            'QUICKNODE_MAX_BUNDLE_SIZE': 'quicknode_bundles.max_bundle_size',
            'QUICKNODE_BUNDLE_TIMEOUT': 'quicknode_bundles.bundle_timeout',
            'QUICKNODE_PRIORITY_FEE': 'quicknode_bundles.priority_fee_lamports',
            'QUICKNODE_FALLBACK_JITO': 'quicknode_bundles.fallback_to_jito',

            # QuickNode Price Feeds Configuration - 🔧 PHASE 2: NEW
            'QUICKNODE_PRICE_FEEDS_ENABLED': 'quicknode_price_feeds.enabled',
            'QUICKNODE_PRICE_API_URL': 'quicknode_price_feeds.api_url',
            'QUICKNODE_PRICE_CACHE': 'quicknode_price_feeds.cache_duration_seconds',
            'QUICKNODE_PRICE_TIMEOUT': 'quicknode_price_feeds.timeout_seconds',
            'QUICKNODE_PRICE_RETRIES': 'quicknode_price_feeds.retry_attempts',
            'QUICKNODE_PRICE_FALLBACK_JUPITER': 'quicknode_price_feeds.fallback_to_jupiter',
            'QUICKNODE_PRICE_FALLBACK_COINGECKO': 'quicknode_price_feeds.fallback_to_coingecko',

            # Jupiter Configuration
            'JUPITER_API_URL': 'dex.jupiter.api_url',
            'JUPITER_SLIPPAGE_BPS': 'dex.jupiter.default_slippage_bps',
            'JUPITER_TIMEOUT': 'dex.jupiter.timeout_seconds',

            # Trading Configuration
            'TRADING_ENABLED': 'mode.live_trading',
            'PAPER_TRADING': 'mode.paper_trading',
            'DRY_RUN': 'mode.dry_run',

            # Wallet Configuration
            'WALLET_ADDRESS': 'wallet.address',
            'WALLET_PRIVATE_KEY': 'wallet.private_key',
            'KEYPAIR_PATH': 'wallet.keypair_path',

            # Risk Management
            'MAX_POSITION_SIZE': 'risk.max_position_size',
            'SLIPPAGE_TOLERANCE': 'execution.slippage_tolerance',
            'MIN_LIQUIDITY_USD': 'execution.min_liquidity_usd',

            # Telegram
            'TELEGRAM_BOT_TOKEN': 'notifications.telegram.bot_token',
            'TELEGRAM_CHAT_ID': 'notifications.telegram.chat_id',
        }

        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                self._set_nested_value(self.config, config_path, env_value)

    def _set_nested_value(self, config: Dict, path: str, value: Any):
        """Set a nested configuration value using dot notation."""
        keys = path.split('.')
        current = config

        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        # Convert string values to appropriate types
        if isinstance(value, str):
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif value.replace('.', '').isdigit():
                value = float(value)

        current[keys[-1]] = value

    def _set_computed_values(self):
        """Set computed configuration values."""
        # Ensure nested structures exist
        self._ensure_nested_structure()

        # Set default URLs if not provided
        if not self.config.get('dex', {}).get('jupiter', {}).get('api_url'):
            self._set_nested_value(self.config, 'dex.jupiter.api_url', 'https://quote-api.jup.ag/v6')

        if not self.config.get('rpc', {}).get('jito_url'):
            self._set_nested_value(self.config, 'rpc.jito_url', 'https://mainnet.block-engine.jito.wtf/api/v1')

        # Set computed RPC URLs with API keys
        helius_key = self.config.get('apis', {}).get('helius', {}).get('api_key')
        if helius_key and not self.config.get('rpc', {}).get('primary_url'):
            primary_url = f"https://mainnet.helius-rpc.com/?api-key={helius_key}"
            self._set_nested_value(self.config, 'rpc.primary_url', primary_url)

        # Set token mappings
        if not self.config.get('tokens'):
            self.config['tokens'] = {
                'SOL': 'So11111111111111111111111111111111111111112',
                'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
            }

    def _ensure_nested_structure(self):
        """Ensure all required nested structures exist."""
        required_structures = [
            'rpc', 'dex.jupiter', 'apis.helius', 'apis.quicknode',
            'wallet', 'risk', 'execution', 'notifications.telegram',
            'mode', 'timeouts', 'circuit_breaker', 'quicknode_bundles',
            'quicknode_price_feeds'  # 🔧 PHASE 2: QuickNode price feeds
        ]

        for structure in required_structures:
            keys = structure.split('.')
            current = self.config
            for key in keys:
                if key not in current:
                    current[key] = {}
                current = current[key]

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration when config file is not found."""
        return {
            'rpc': {
                'primary_url': 'https://api.mainnet-beta.solana.com',
                'fallback_url': 'https://api.mainnet-beta.solana.com',
                'jito_url': 'https://mainnet.block-engine.jito.wtf/api/v1',
                'commitment': 'confirmed',
                'max_retries': 3,
                'timeout': 30
            },
            'dex': {
                'jupiter': {
                    'api_url': 'https://quote-api.jup.ag/v6',
                    'default_slippage_bps': 50,
                    'timeout_seconds': 5,
                    'max_accounts': 20,
                    'auto_slippage': True
                }
            },
            'execution': {
                'slippage_tolerance': 0.02,
                'min_liquidity_usd': 50000,
                'use_jito': True,
                'priority_fee_lamports': 5000
            },
            'timeouts': {
                'http_client': 30.0,
                'jupiter_quote': 5.0,
                'transaction_confirmation': 30.0,
                'bundle_confirmation': 30.0
            },
            'circuit_breaker': {
                'failure_threshold': 3,
                'reset_timeout': 60,
                'enabled': True
            }
        }

    def _validate_config(self):
        """Validate the loaded configuration."""
        required_fields = [
            'wallet.address',
            'apis.helius.api_key'
        ]

        missing_fields = []
        for field in required_fields:
            if not self.get(field):
                missing_fields.append(field)

        if missing_fields:
            logger.warning(f"⚠️ Missing configuration fields: {missing_fields}")

    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        # Don't auto-load here to prevent recursion
        if not self.config:
            return default

        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def get_rpc_config(self) -> Dict[str, Any]:
        """🔧 UPGRADED: Get RPC configuration with QuickNode as primary."""
        return {
            'primary_rpc': self.get('rpc.primary_url'),  # QuickNode RPC
            'fallback_rpc': self.get('rpc.fallback_url'),  # Helius RPC
            'jito_rpc': self.get('rpc.jito_url'),
            'quicknode_bundle_url': self.get('rpc.quicknode_bundle_url'),  # 🔧 NEW
            'helius_api_key': self.get('apis.helius.api_key'),
            'quicknode_api_key': self.get('apis.quicknode.api_key'),
            'timeout': self.get('timeouts.http_client', 30.0),
            'max_retries': self.get('rpc.max_retries', 3)
        }

    def get_quicknode_config(self) -> Dict[str, Any]:
        """🔧 PHASE 1: Get QuickNode bundle configuration."""
        return {
            'enabled': self.get('quicknode_bundles.enabled', True),
            'api_url': self.get('rpc.quicknode_bundle_url', 'https://api.quicknode.com/v1/solana/mainnet/bundles'),
            'api_key': self.get('apis.quicknode.api_key'),
            'max_bundle_size': self.get('quicknode_bundles.max_bundle_size', 5),
            'bundle_timeout': self.get('quicknode_bundles.bundle_timeout', 30),
            'priority_fee_lamports': self.get('quicknode_bundles.priority_fee_lamports', 20000),
            'retry_attempts': self.get('quicknode_bundles.retry_attempts', 3),
            'fallback_to_jito': self.get('quicknode_bundles.fallback_to_jito', True)
        }

    def get_quicknode_price_feeds_config(self) -> Dict[str, Any]:
        """🔧 PHASE 2: Get QuickNode price feeds configuration."""
        return {
            'enabled': self.get('quicknode_price_feeds.enabled', True),
            'api_url': self.get('quicknode_price_feeds.api_url', 'https://api.quicknode.com/v1/solana/mainnet/prices'),
            'api_key': self.get('apis.quicknode.api_key'),
            'cache_duration_seconds': self.get('quicknode_price_feeds.cache_duration_seconds', 30),
            'timeout_seconds': self.get('quicknode_price_feeds.timeout_seconds', 10),
            'retry_attempts': self.get('quicknode_price_feeds.retry_attempts', 3),
            'fallback_to_jupiter': self.get('quicknode_price_feeds.fallback_to_jupiter', True),
            'fallback_to_coingecko': self.get('quicknode_price_feeds.fallback_to_coingecko', True)
        }

    def get_jupiter_config(self) -> Dict[str, Any]:
        """Get Jupiter configuration."""
        return {
            'api_url': self.get('dex.jupiter.api_url'),
            'default_slippage_bps': self.get('dex.jupiter.default_slippage_bps', 50),
            'timeout_seconds': self.get('timeouts.jupiter_quote', 5.0),
            'max_accounts': self.get('dex.jupiter.max_accounts', 20),
            'auto_slippage': self.get('dex.jupiter.auto_slippage', True)
        }

    def get_execution_config(self) -> Dict[str, Any]:
        """Get execution configuration."""
        return {
            'slippage_tolerance': self.get('execution.slippage_tolerance', 0.02),
            'min_liquidity_usd': self.get('execution.min_liquidity_usd', 50000),
            'use_jito': self.get('execution.use_jito', True),
            'priority_fee_lamports': self.get('execution.priority_fee_lamports', 5000),
            'circuit_breaker_enabled': self.get('circuit_breaker.enabled', True),
            'failure_threshold': self.get('circuit_breaker.failure_threshold', 3),
            'reset_timeout': self.get('circuit_breaker.reset_timeout', 60)
        }

# Global configuration instance
_unified_config = None

def get_unified_config() -> UnifiedConfig:
    """Get the global unified configuration instance."""
    global _unified_config
    if _unified_config is None:
        _unified_config = UnifiedConfig()
    return _unified_config

def load_unified_config() -> Dict[str, Any]:
    """Load and return the unified configuration."""
    config_instance = get_unified_config()
    if not config_instance._loaded:
        config_instance.load()
    return config_instance.config

#!/usr/bin/env python3
"""
Jupiter Swap Fallback
Provides Jupiter-based swap functionality when Orca fails.
"""

import logging
import asyncio
import httpx
import json
import base64
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class JupiterSwapFallback:
    """Provides Jupiter swap functionality as fallback."""

    def __init__(self):
        """Initialize Jupiter swap fallback."""
        self.base_url = "https://quote-api.jup.ag/v6"
        self.client = None

    async def get_quote(self, input_mint: str, output_mint: str, amount: int, slippage_bps: int = 50) -> Optional[Dict[str, Any]]:
        """Get a swap quote from Jupiter."""
        try:
            if not self.client:
                self.client = httpx.AsyncClient(timeout=30.0)

            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": amount,
                "slippageBps": slippage_bps,
                "onlyDirectRoutes": "false",
                "asLegacyTransaction": "true"  # Use legacy transaction format
            }

            response = await self.client.get(f"{self.base_url}/quote", params=params)
            response.raise_for_status()

            quote_data = response.json()
            logger.info(f"✅ Jupiter quote received: {quote_data.get('outAmount', 'unknown')} output tokens")

            return quote_data

        except Exception as e:
            logger.error(f"❌ Jupiter quote failed: {e}")
            return None

    async def get_swap_transaction(self, quote: Dict[str, Any], user_public_key: str) -> Optional[str]:
        """Get swap transaction from Jupiter."""
        try:
            if not self.client:
                self.client = httpx.AsyncClient(timeout=30.0)

            swap_request = {
                "quoteResponse": quote,
                "userPublicKey": user_public_key,
                "wrapAndUnwrapSol": True,
                "asLegacyTransaction": True  # Use legacy transaction format
            }

            response = await self.client.post(
                f"{self.base_url}/swap",
                json=swap_request,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            swap_data = response.json()

            if "swapTransaction" in swap_data:
                logger.info("✅ Jupiter swap transaction received")
                return swap_data["swapTransaction"]
            else:
                logger.error("❌ No swap transaction in Jupiter response")
                return None

        except Exception as e:
            logger.error(f"❌ Jupiter swap transaction failed: {e}")
            return None

    async def build_simple_swap(self, signal: Dict[str, Any], wallet_pubkey: str, keypair=None) -> Optional[str]:
        """Build and sign a simple swap transaction using Jupiter."""
        try:
            # Map market to token addresses
            token_mapping = {
                "SOL": "So11111111111111111111111111111111111111112",
                "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "BONK": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
                "JUP": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN"
            }

            market = signal.get("market", "")
            action = signal.get("action", "")
            size = signal.get("size", 0.01)

            # Parse market (e.g., "BONK-USDC")
            if "-" in market:
                base_token, quote_token = market.split("-")
            else:
                logger.error(f"Invalid market format: {market}")
                return None

            # Determine input/output tokens based on action
            if action == "BUY":
                input_token = quote_token  # Buy BONK with USDC
                output_token = base_token
                # Convert size to lamports/smallest unit
                if input_token == "USDC":
                    amount = int(size * 1_000_000)  # USDC has 6 decimals
                else:
                    amount = int(size * 1_000_000_000)  # SOL has 9 decimals
            else:  # SELL
                input_token = base_token  # Sell BONK for USDC
                output_token = quote_token
                amount = int(size * 1_000_000_000)  # Assume 9 decimals for most tokens

            input_mint = token_mapping.get(input_token)
            output_mint = token_mapping.get(output_token)

            if not input_mint or not output_mint:
                logger.error(f"Unknown tokens: {input_token} -> {output_token}")
                return None

            logger.info(f"🔄 Jupiter swap: {amount} {input_token} -> {output_token}")

            # Get quote
            quote = await self.get_quote(input_mint, output_mint, amount)
            if not quote:
                return None

            # Get swap transaction (unsigned)
            unsigned_tx = await self.get_swap_transaction(quote, wallet_pubkey)
            if not unsigned_tx:
                return None

            # 🔧 ENHANCED: Sign the transaction with fresh blockhash handling
            if keypair:
                try:
                    import base64
                    from solders.transaction import Transaction
                    from solders.hash import Hash
                    import httpx

                    # Decode the base64 transaction
                    tx_bytes = base64.b64decode(unsigned_tx)
                    transaction = Transaction.from_bytes(tx_bytes)

                    # 🔧 ENHANCED: Get fresh blockhash for signing
                    fresh_blockhash = await self._get_fresh_blockhash()
                    if not fresh_blockhash:
                        logger.error("❌ Failed to get fresh blockhash for signing")
                        return None

                    # 🔧 ENHANCED: Sign the transaction with fresh blockhash
                    transaction.sign([keypair], fresh_blockhash)

                    # Return signed transaction as base64
                    signed_tx = base64.b64encode(bytes(transaction)).decode('utf-8')
                    logger.info("✅ Jupiter transaction signed successfully with fresh blockhash")
                    return signed_tx

                except Exception as e:
                    logger.error(f"❌ Failed to sign Jupiter transaction: {e}")
                    return None
            else:
                logger.warning("⚠️ No keypair provided - returning unsigned transaction")
                return unsigned_tx

        except Exception as e:
            logger.error(f"❌ Jupiter swap building failed: {e}")
            return None

    async def _get_fresh_blockhash(self):
        """🔧 ENHANCED: Get fresh blockhash for transaction signing."""
        try:
            from solders.hash import Hash

            # Use Helius RPC to get fresh blockhash
            helius_url = "https://mainnet.helius-rpc.com/?api-key=dda9f776-9a40-447d-9ca4-22a27c21169e"

            if not self.client:
                self.client = httpx.AsyncClient(timeout=10.0)

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "processed"}]
            }

            response = await self.client.post(helius_url, json=payload)
            response.raise_for_status()
            result = response.json()

            if 'result' in result and 'value' in result['result'] and 'blockhash' in result['result']['value']:
                blockhash_str = result['result']['value']['blockhash']
                fresh_blockhash = Hash.from_string(blockhash_str)
                logger.info(f"✅ Fresh blockhash obtained: {blockhash_str}")
                return fresh_blockhash
            else:
                logger.error(f"Failed to get fresh blockhash: {result.get('error')}")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting fresh blockhash: {e}")
            return None

    async def close(self):
        """Close the HTTP client."""
        if self.client:
            await self.client.aclose()

# Global instance
_jupiter_fallback = None

def get_jupiter_fallback() -> JupiterSwapFallback:
    """Get the global Jupiter fallback instance."""
    global _jupiter_fallback
    if _jupiter_fallback is None:
        _jupiter_fallback = JupiterSwapFallback()
    return _jupiter_fallback

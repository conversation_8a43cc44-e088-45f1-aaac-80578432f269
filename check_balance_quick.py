#!/usr/bin/env python3
"""
Quick Balance Checker
Checks current wallet balance and token holdings.
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

async def check_wallet_balance():
    """Check current wallet balance and token holdings."""
    print("💰 RWA Trading System - Wallet Balance Check")
    print("="*50)

    try:
        from phase_4_deployment.apis.helius_client import HeliusClient
        from solders.pubkey import Pubkey

        # Initialize client
        client = HeliusClient()

        # Wallet address
        wallet_str = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
        wallet = Pubkey.from_string(wallet_str)

        print(f"📍 Wallet: {wallet_str}")
        print()

        # Check balances using the client's method
        try:
            balances = await client.get_balances(wallet_str)

            # Extract SOL balance
            sol_balance = 0
            usdc_balance = 0

            for token in balances.get('tokens', []):
                if token['mint'] == 'So11111111111111111111111111111111111111112':
                    sol_balance = token['amount'] / 1e9  # Convert lamports to SOL
                elif token['mint'] == 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v':
                    usdc_balance = token['amount'] / 1e6  # Convert to USDC

            # Also check native SOL balance
            if 'nativeBalance' in balances:
                sol_balance = balances['nativeBalance'] / 1e9

            print(f"💎 SOL Balance: {sol_balance:.9f} SOL")
            print(f"💵 SOL Value: ${sol_balance * 174.5:.2f} USD (approx)")
            print(f"💵 USDC Balance: {usdc_balance:.6f} USDC")

        except Exception as e:
            print(f"⚠️ Error checking balances: {e}")
            # Fallback to simple balance check
            try:
                from phase_4_deployment.rpc_execution.helius_client import HeliusClient as SimpleClient
                simple_client = SimpleClient()
                sol_balance = await simple_client.get_balance(wallet)
                print(f"💎 SOL Balance (fallback): {sol_balance:.9f} SOL")
            except Exception as e2:
                print(f"⚠️ Fallback balance check also failed: {e2}")

        print()
        print("✅ Balance check completed successfully")
        return True

    except Exception as e:
        print(f"❌ Error checking wallet balance: {e}")
        return False

async def main():
    """Main function."""
    success = await check_wallet_balance()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
